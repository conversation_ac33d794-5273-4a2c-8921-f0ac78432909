// In-memory storage for demo purposes when MongoDB is not available
const bcrypt = require('bcryptjs');

class MemoryStorage {
  constructor() {
    this.users = new Map();
    this.messages = [];
    this.userIdCounter = 1;
    this.messageIdCounter = 1;
  }

  // User methods
  async createUser(userData) {
    const { username, email, password } = userData;
    
    // Check if user already exists
    for (const user of this.users.values()) {
      if (user.email === email || user.username === username) {
        throw new Error('User with this email or username already exists');
      }
    }

    // Hash password
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(password, salt);

    const user = {
      _id: this.userIdCounter++,
      username,
      email,
      password: hashedPassword,
      status: 'Available',
      isOnline: false,
      lastSeen: new Date(),
      createdAt: new Date(),
      updatedAt: new Date()
    };

    this.users.set(user._id, user);
    return this.sanitizeUser(user);
  }

  async findUserByEmail(email) {
    for (const user of this.users.values()) {
      if (user.email === email) {
        return user;
      }
    }
    return null;
  }

  async findUserById(id) {
    return this.users.get(parseInt(id)) || null;
  }

  async updateUser(id, updates) {
    const user = this.users.get(parseInt(id));
    if (!user) return null;

    Object.assign(user, updates, { updatedAt: new Date() });
    this.users.set(parseInt(id), user);
    return this.sanitizeUser(user);
  }

  async getAllUsers(excludeId) {
    const users = Array.from(this.users.values())
      .filter(user => user._id !== parseInt(excludeId))
      .map(user => this.sanitizeUser(user));
    return users;
  }

  async comparePassword(candidatePassword, hashedPassword) {
    return await bcrypt.compare(candidatePassword, hashedPassword);
  }

  // Message methods
  async createMessage(messageData) {
    const message = {
      _id: this.messageIdCounter++,
      sender: { _id: messageData.sender, username: this.getUsernameById(messageData.sender) },
      recipient: { _id: messageData.recipient, username: this.getUsernameById(messageData.recipient) },
      content: messageData.content,
      messageType: messageData.messageType || 'text',
      isRead: false,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    this.messages.push(message);
    return message;
  }

  async getMessages(userId1, userId2) {
    return this.messages.filter(msg => 
      (msg.sender._id === parseInt(userId1) && msg.recipient._id === parseInt(userId2)) ||
      (msg.sender._id === parseInt(userId2) && msg.recipient._id === parseInt(userId1))
    ).slice(-100); // Return last 100 messages
  }

  async markMessagesAsRead(senderId, recipientId) {
    this.messages.forEach(msg => {
      if (msg.sender._id === parseInt(senderId) && msg.recipient._id === parseInt(recipientId) && !msg.isRead) {
        msg.isRead = true;
        msg.readAt = new Date();
      }
    });
  }

  async getUnreadCount(userId) {
    return this.messages.filter(msg => 
      msg.recipient._id === parseInt(userId) && !msg.isRead
    ).length;
  }

  // Helper methods
  sanitizeUser(user) {
    const { password, ...userWithoutPassword } = user;
    return userWithoutPassword;
  }

  getUsernameById(id) {
    const user = this.users.get(parseInt(id));
    return user ? user.username : 'Unknown';
  }

  // Initialize with some demo users
  async initializeDemoData() {
    try {
      await this.createUser({
        username: 'demo1',
        email: '<EMAIL>',
        password: 'password123'
      });

      await this.createUser({
        username: 'demo2',
        email: '<EMAIL>',
        password: 'password123'
      });

      console.log('Demo users created: demo1@example.<NAME_EMAIL> (password: password123)');
    } catch (error) {
      // Users might already exist
    }
  }
}

module.exports = new MemoryStorage();
