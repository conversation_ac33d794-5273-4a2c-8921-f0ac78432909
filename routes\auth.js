const express = require('express');
const jwt = require('jsonwebtoken');
const User = require('../models/User');
const auth = require('../middleware/auth');
const memoryStorage = require('../utils/memoryStorage');

const router = express.Router();

// Generate JWT token
const generateToken = (userId) => {
  return jwt.sign({ userId }, process.env.JWT_SECRET, { expiresIn: '7d' });
};

// Check if MongoDB is connected
const isMongoConnected = () => {
  return require('mongoose').connection.readyState === 1;
};

// Register user
router.post('/register', async (req, res) => {
  try {
    const { username, email, password } = req.body;

    if (isMongoConnected()) {
      // Use MongoDB
      const existingUser = await User.findOne({
        $or: [{ email }, { username }]
      });

      if (existingUser) {
        return res.status(400).json({
          message: 'User with this email or username already exists'
        });
      }

      const user = new User({
        username,
        email,
        password
      });

      await user.save();

      const token = generateToken(user._id);

      res.status(201).json({
        message: 'User created successfully',
        token,
        user: {
          id: user._id,
          username: user.username,
          email: user.email,
          status: user.status,
          avatar: user.avatar
        }
      });
    } else {
      // Use memory storage
      const user = await memoryStorage.createUser({ username, email, password });
      const token = generateToken(user._id);

      res.status(201).json({
        message: 'User created successfully (demo mode)',
        token,
        user: {
          id: user._id,
          username: user.username,
          email: user.email,
          status: user.status,
          avatar: user.avatar || ''
        }
      });
    }
  } catch (error) {
    console.error('Registration error:', error);
    res.status(500).json({ message: error.message || 'Server error during registration' });
  }
});

// Login user
router.post('/login', async (req, res) => {
  try {
    const { email, password } = req.body;

    if (isMongoConnected()) {
      // Use MongoDB
      const user = await User.findOne({ email });
      if (!user) {
        return res.status(400).json({ message: 'Invalid credentials' });
      }

      const isMatch = await user.comparePassword(password);
      if (!isMatch) {
        return res.status(400).json({ message: 'Invalid credentials' });
      }

      user.isOnline = true;
      user.lastSeen = new Date();
      await user.save();

      const token = generateToken(user._id);

      res.json({
        message: 'Login successful',
        token,
        user: {
          id: user._id,
          username: user.username,
          email: user.email,
          status: user.status,
          avatar: user.avatar,
          isOnline: user.isOnline
        }
      });
    } else {
      // Use memory storage
      const user = await memoryStorage.findUserByEmail(email);
      if (!user) {
        return res.status(400).json({ message: 'Invalid credentials' });
      }

      const isMatch = await memoryStorage.comparePassword(password, user.password);
      if (!isMatch) {
        return res.status(400).json({ message: 'Invalid credentials' });
      }

      await memoryStorage.updateUser(user._id, { isOnline: true, lastSeen: new Date() });
      const token = generateToken(user._id);

      res.json({
        message: 'Login successful (demo mode)',
        token,
        user: {
          id: user._id,
          username: user.username,
          email: user.email,
          status: user.status,
          avatar: user.avatar || '',
          isOnline: true
        }
      });
    }
  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({ message: 'Server error during login' });
  }
});

// Get current user
router.get('/me', auth, async (req, res) => {
  try {
    if (isMongoConnected()) {
      res.json({
        user: {
          id: req.user._id,
          username: req.user.username,
          email: req.user.email,
          status: req.user.status,
          avatar: req.user.avatar,
          isOnline: req.user.isOnline
        }
      });
    } else {
      const user = await memoryStorage.findUserById(req.user._id);
      if (!user) {
        return res.status(404).json({ message: 'User not found' });
      }

      res.json({
        user: {
          id: user._id,
          username: user.username,
          email: user.email,
          status: user.status,
          avatar: user.avatar || '',
          isOnline: user.isOnline
        }
      });
    }
  } catch (error) {
    console.error('Get user error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Logout user
router.post('/logout', auth, async (req, res) => {
  try {
    // Update user offline status
    req.user.isOnline = false;
    req.user.lastSeen = new Date();
    await req.user.save();

    res.json({ message: 'Logout successful' });
  } catch (error) {
    console.error('Logout error:', error);
    res.status(500).json({ message: 'Server error during logout' });
  }
});

module.exports = router;
