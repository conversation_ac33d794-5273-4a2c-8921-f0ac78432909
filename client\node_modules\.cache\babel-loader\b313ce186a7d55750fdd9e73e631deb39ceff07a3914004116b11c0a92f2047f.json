{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\yaaa\\\\client\\\\src\\\\components\\\\Chat\\\\ChatDashboard.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport ChatList from './ChatList';\nimport ChatWindow from './ChatWindow';\nimport Header from '../Layout/Header';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ChatDashboard = () => {\n  _s();\n  const [selectedChat, setSelectedChat] = useState(null);\n  const handleSelectChat = user => {\n    setSelectedChat(user);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"h-screen flex flex-col bg-whatsapp-gray\",\n    children: [/*#__PURE__*/_jsxDEV(<PERSON>er, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 15,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 flex flex-col md:flex-row overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: `${selectedChat ? 'hidden md:flex' : 'flex'} md:w-1/3`,\n        children: /*#__PURE__*/_jsxDEV(ChatList, {\n          onSelectChat: handleSelectChat,\n          selectedChat: selectedChat\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 19,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 18,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `${selectedChat ? 'flex' : 'hidden md:flex'} flex-1`,\n        children: /*#__PURE__*/_jsxDEV(ChatWindow, {\n          selectedChat: selectedChat,\n          onBack: () => setSelectedChat(null)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 25,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 24,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 17,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 14,\n    columnNumber: 5\n  }, this);\n};\n_s(ChatDashboard, \"yAh2Quu2hIzBBSpE19QrhxIg+PE=\");\n_c = ChatDashboard;\nexport default ChatDashboard;\nvar _c;\n$RefreshReg$(_c, \"ChatDashboard\");", "map": {"version": 3, "names": ["React", "useState", "ChatList", "ChatWindow", "Header", "jsxDEV", "_jsxDEV", "ChatDashboard", "_s", "selectedC<PERSON>", "setSelectedChat", "handleSelectChat", "user", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSelectChat", "onBack", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/yaaa/client/src/components/Chat/ChatDashboard.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport ChatList from './ChatList';\nimport ChatWindow from './ChatWindow';\nimport Header from '../Layout/Header';\n\nconst ChatDashboard = () => {\n  const [selectedChat, setSelectedChat] = useState(null);\n\n  const handleSelectChat = (user) => {\n    setSelectedChat(user);\n  };\n\n  return (\n    <div className=\"h-screen flex flex-col bg-whatsapp-gray\">\n      <Header />\n      \n      <div className=\"flex-1 flex flex-col md:flex-row overflow-hidden\">\n        <div className={`${selectedChat ? 'hidden md:flex' : 'flex'} md:w-1/3`}>\n          <ChatList\n            onSelectChat={handleSelectChat}\n            selectedChat={selectedChat}\n          />\n        </div>\n        <div className={`${selectedChat ? 'flex' : 'hidden md:flex'} flex-1`}>\n          <ChatWindow\n            selectedChat={selectedChat}\n            onBack={() => setSelectedChat(null)}\n          />\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default ChatDashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,QAAQ,MAAM,YAAY;AACjC,OAAOC,UAAU,MAAM,cAAc;AACrC,OAAOC,MAAM,MAAM,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtC,MAAMC,aAAa,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC1B,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGT,QAAQ,CAAC,IAAI,CAAC;EAEtD,MAAMU,gBAAgB,GAAIC,IAAI,IAAK;IACjCF,eAAe,CAACE,IAAI,CAAC;EACvB,CAAC;EAED,oBACEN,OAAA;IAAKO,SAAS,EAAC,yCAAyC;IAAAC,QAAA,gBACtDR,OAAA,CAACF,MAAM;MAAAW,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAEVZ,OAAA;MAAKO,SAAS,EAAC,kDAAkD;MAAAC,QAAA,gBAC/DR,OAAA;QAAKO,SAAS,EAAE,GAAGJ,YAAY,GAAG,gBAAgB,GAAG,MAAM,WAAY;QAAAK,QAAA,eACrER,OAAA,CAACJ,QAAQ;UACPiB,YAAY,EAAER,gBAAiB;UAC/BF,YAAY,EAAEA;QAAa;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eACNZ,OAAA;QAAKO,SAAS,EAAE,GAAGJ,YAAY,GAAG,MAAM,GAAG,gBAAgB,SAAU;QAAAK,QAAA,eACnER,OAAA,CAACH,UAAU;UACTM,YAAY,EAAEA,YAAa;UAC3BW,MAAM,EAAEA,CAAA,KAAMV,eAAe,CAAC,IAAI;QAAE;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACV,EAAA,CA3BID,aAAa;AAAAc,EAAA,GAAbd,aAAa;AA6BnB,eAAeA,aAAa;AAAC,IAAAc,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}