const express = require('express');
const User = require('../models/User');
const auth = require('../middleware/auth');
const memoryStorage = require('../utils/memoryStorage');

const router = express.Router();

const isMongoConnected = () => {
  return require('mongoose').connection.readyState === 1;
};

// Get all users (for chat list)
router.get('/', auth, async (req, res) => {
  try {
    if (isMongoConnected()) {
      const users = await User.find({ _id: { $ne: req.user._id } })
        .select('username email status avatar isOnline lastSeen')
        .sort({ username: 1 });
      res.json(users);
    } else {
      const users = await memoryStorage.getAllUsers(req.user._id);
      res.json(users);
    }
  } catch (error) {
    console.error('Get users error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Update user status
router.put('/status', auth, async (req, res) => {
  try {
    const { status } = req.body;

    if (!['Available', 'Busy', 'Away', 'Do not disturb'].includes(status)) {
      return res.status(400).json({ message: 'Invalid status' });
    }

    if (isMongoConnected()) {
      req.user.status = status;
      await req.user.save();

      res.json({
        message: 'Status updated successfully',
        status: req.user.status
      });
    } else {
      const user = await memoryStorage.updateUser(req.user._id, { status });
      res.json({
        message: 'Status updated successfully',
        status: user.status
      });
    }
  } catch (error) {
    console.error('Update status error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Update user profile
router.put('/profile', auth, async (req, res) => {
  try {
    const { username, avatar } = req.body;

    if (username) {
      // Check if username is already taken
      const existingUser = await User.findOne({
        username,
        _id: { $ne: req.user._id }
      });

      if (existingUser) {
        return res.status(400).json({ message: 'Username already taken' });
      }

      req.user.username = username;
    }

    if (avatar !== undefined) {
      req.user.avatar = avatar;
    }

    await req.user.save();

    res.json({
      message: 'Profile updated successfully',
      user: {
        id: req.user._id,
        username: req.user.username,
        email: req.user.email,
        status: req.user.status,
        avatar: req.user.avatar
      }
    });
  } catch (error) {
    console.error('Update profile error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

module.exports = router;
