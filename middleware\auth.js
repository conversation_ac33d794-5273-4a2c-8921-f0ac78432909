const jwt = require('jsonwebtoken');
const User = require('../models/User');
const memoryStorage = require('../utils/memoryStorage');

const isMongoConnected = () => {
  return require('mongoose').connection.readyState === 1;
};

const auth = async (req, res, next) => {
  try {
    const token = req.header('Authorization')?.replace('Bearer ', '');

    if (!token) {
      return res.status(401).json({ message: 'No token, authorization denied' });
    }

    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    let user;

    if (isMongoConnected()) {
      user = await User.findById(decoded.userId).select('-password');
    } else {
      user = await memoryStorage.findUserById(decoded.userId);
    }

    if (!user) {
      return res.status(401).json({ message: 'Token is not valid' });
    }

    req.user = user;
    next();
  } catch (error) {
    res.status(401).json({ message: 'Token is not valid' });
  }
};

module.exports = auth;
