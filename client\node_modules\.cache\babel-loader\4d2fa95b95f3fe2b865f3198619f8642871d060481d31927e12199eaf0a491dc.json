{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\yaaa\\\\client\\\\src\\\\contexts\\\\SocketContext.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useEffect, useState } from 'react';\nimport io from 'socket.io-client';\nimport { useAuth } from './AuthContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SocketContext = /*#__PURE__*/createContext();\nexport const useSocket = () => {\n  _s();\n  const context = useContext(SocketContext);\n  if (!context) {\n    throw new Error('useSocket must be used within a SocketProvider');\n  }\n  return context;\n};\n_s(useSocket, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport const SocketProvider = ({\n  children\n}) => {\n  _s2();\n  const [socket, setSocket] = useState(null);\n  const [onlineUsers, setOnlineUsers] = useState([]);\n  const [messages, setMessages] = useState([]);\n  const [typingUsers, setTypingUsers] = useState({});\n  const {\n    user\n  } = useAuth();\n  useEffect(() => {\n    if (user) {\n      const newSocket = io('http://localhost:5000');\n      newSocket.emit('join', user.id);\n\n      // Listen for online users\n      newSocket.on('onlineUsers', users => {\n        setOnlineUsers(users);\n      });\n\n      // Listen for user online/offline status\n      newSocket.on('userOnline', userId => {\n        setOnlineUsers(prev => [...prev, userId]);\n      });\n      newSocket.on('userOffline', userId => {\n        setOnlineUsers(prev => prev.filter(id => id !== userId));\n      });\n\n      // Listen for private messages\n      newSocket.on('privateMessage', messageData => {\n        setMessages(prev => [...prev, messageData]);\n      });\n\n      // Listen for typing indicators\n      newSocket.on('typing', ({\n        senderId,\n        isTyping\n      }) => {\n        setTypingUsers(prev => ({\n          ...prev,\n          [senderId]: isTyping\n        }));\n      });\n\n      // Listen for user status updates\n      newSocket.on('userStatusUpdate', ({\n        userId,\n        status\n      }) => {\n        // Handle user status updates if needed\n        console.log(`User ${userId} status updated to ${status}`);\n      });\n      setSocket(newSocket);\n      return () => {\n        newSocket.close();\n      };\n    }\n  }, [user]);\n  const sendMessage = (recipientId, message) => {\n    if (socket && user) {\n      socket.emit('privateMessage', {\n        recipientId,\n        message,\n        senderId: user.id\n      });\n\n      // Add message to local state immediately\n      const messageData = {\n        senderId: user.id,\n        message,\n        timestamp: new Date()\n      };\n      setMessages(prev => [...prev, messageData]);\n    }\n  };\n  const sendTypingIndicator = (recipientId, isTyping) => {\n    if (socket) {\n      socket.emit('typing', {\n        recipientId,\n        isTyping\n      });\n    }\n  };\n  const updateStatus = status => {\n    if (socket && user) {\n      socket.emit('updateStatus', {\n        userId: user.id,\n        status\n      });\n    }\n  };\n  const value = {\n    socket,\n    onlineUsers,\n    messages,\n    typingUsers,\n    sendMessage,\n    sendTypingIndicator,\n    updateStatus,\n    setMessages\n  };\n  return /*#__PURE__*/_jsxDEV(SocketContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 111,\n    columnNumber: 5\n  }, this);\n};\n_s2(SocketProvider, \"rLYby+ddsxc/TSTAfhxkc1jERR8=\", false, function () {\n  return [useAuth];\n});\n_c = SocketProvider;\nvar _c;\n$RefreshReg$(_c, \"SocketProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useEffect", "useState", "io", "useAuth", "jsxDEV", "_jsxDEV", "SocketContext", "useSocket", "_s", "context", "Error", "SocketProvider", "children", "_s2", "socket", "setSocket", "onlineUsers", "setOnlineUsers", "messages", "setMessages", "typingUsers", "setTypingUsers", "user", "newSocket", "emit", "id", "on", "users", "userId", "prev", "filter", "messageData", "senderId", "isTyping", "status", "console", "log", "close", "sendMessage", "recipientId", "message", "timestamp", "Date", "sendTypingIndicator", "updateStatus", "value", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/yaaa/client/src/contexts/SocketContext.js"], "sourcesContent": ["import React, { createContext, useContext, useEffect, useState } from 'react';\nimport io from 'socket.io-client';\nimport { useAuth } from './AuthContext';\n\nconst SocketContext = createContext();\n\nexport const useSocket = () => {\n  const context = useContext(SocketContext);\n  if (!context) {\n    throw new Error('useSocket must be used within a SocketProvider');\n  }\n  return context;\n};\n\nexport const SocketProvider = ({ children }) => {\n  const [socket, setSocket] = useState(null);\n  const [onlineUsers, setOnlineUsers] = useState([]);\n  const [messages, setMessages] = useState([]);\n  const [typingUsers, setTypingUsers] = useState({});\n  const { user } = useAuth();\n\n  useEffect(() => {\n    if (user) {\n      const newSocket = io('http://localhost:5000');\n      \n      newSocket.emit('join', user.id);\n      \n      // Listen for online users\n      newSocket.on('onlineUsers', (users) => {\n        setOnlineUsers(users);\n      });\n\n      // Listen for user online/offline status\n      newSocket.on('userOnline', (userId) => {\n        setOnlineUsers(prev => [...prev, userId]);\n      });\n\n      newSocket.on('userOffline', (userId) => {\n        setOnlineUsers(prev => prev.filter(id => id !== userId));\n      });\n\n      // Listen for private messages\n      newSocket.on('privateMessage', (messageData) => {\n        setMessages(prev => [...prev, messageData]);\n      });\n\n      // Listen for typing indicators\n      newSocket.on('typing', ({ senderId, isTyping }) => {\n        setTypingUsers(prev => ({\n          ...prev,\n          [senderId]: isTyping\n        }));\n      });\n\n      // Listen for user status updates\n      newSocket.on('userStatusUpdate', ({ userId, status }) => {\n        // Handle user status updates if needed\n        console.log(`User ${userId} status updated to ${status}`);\n      });\n\n      setSocket(newSocket);\n\n      return () => {\n        newSocket.close();\n      };\n    }\n  }, [user]);\n\n  const sendMessage = (recipientId, message) => {\n    if (socket && user) {\n      socket.emit('privateMessage', {\n        recipientId,\n        message,\n        senderId: user.id\n      });\n      \n      // Add message to local state immediately\n      const messageData = {\n        senderId: user.id,\n        message,\n        timestamp: new Date()\n      };\n      setMessages(prev => [...prev, messageData]);\n    }\n  };\n\n  const sendTypingIndicator = (recipientId, isTyping) => {\n    if (socket) {\n      socket.emit('typing', { recipientId, isTyping });\n    }\n  };\n\n  const updateStatus = (status) => {\n    if (socket && user) {\n      socket.emit('updateStatus', { userId: user.id, status });\n    }\n  };\n\n  const value = {\n    socket,\n    onlineUsers,\n    messages,\n    typingUsers,\n    sendMessage,\n    sendTypingIndicator,\n    updateStatus,\n    setMessages\n  };\n\n  return (\n    <SocketContext.Provider value={value}>\n      {children}\n    </SocketContext.Provider>\n  );\n};\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAC7E,OAAOC,EAAE,MAAM,kBAAkB;AACjC,SAASC,OAAO,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,MAAMC,aAAa,gBAAGR,aAAa,CAAC,CAAC;AAErC,OAAO,MAAMS,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC7B,MAAMC,OAAO,GAAGV,UAAU,CAACO,aAAa,CAAC;EACzC,IAAI,CAACG,OAAO,EAAE;IACZ,MAAM,IAAIC,KAAK,CAAC,gDAAgD,CAAC;EACnE;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,EAAA,CANWD,SAAS;AAQtB,OAAO,MAAMI,cAAc,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,GAAA;EAC9C,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EAC1C,MAAM,CAACe,WAAW,EAAEC,cAAc,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACiB,QAAQ,EAAEC,WAAW,CAAC,GAAGlB,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACmB,WAAW,EAAEC,cAAc,CAAC,GAAGpB,QAAQ,CAAC,CAAC,CAAC,CAAC;EAClD,MAAM;IAAEqB;EAAK,CAAC,GAAGnB,OAAO,CAAC,CAAC;EAE1BH,SAAS,CAAC,MAAM;IACd,IAAIsB,IAAI,EAAE;MACR,MAAMC,SAAS,GAAGrB,EAAE,CAAC,uBAAuB,CAAC;MAE7CqB,SAAS,CAACC,IAAI,CAAC,MAAM,EAAEF,IAAI,CAACG,EAAE,CAAC;;MAE/B;MACAF,SAAS,CAACG,EAAE,CAAC,aAAa,EAAGC,KAAK,IAAK;QACrCV,cAAc,CAACU,KAAK,CAAC;MACvB,CAAC,CAAC;;MAEF;MACAJ,SAAS,CAACG,EAAE,CAAC,YAAY,EAAGE,MAAM,IAAK;QACrCX,cAAc,CAACY,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAED,MAAM,CAAC,CAAC;MAC3C,CAAC,CAAC;MAEFL,SAAS,CAACG,EAAE,CAAC,aAAa,EAAGE,MAAM,IAAK;QACtCX,cAAc,CAACY,IAAI,IAAIA,IAAI,CAACC,MAAM,CAACL,EAAE,IAAIA,EAAE,KAAKG,MAAM,CAAC,CAAC;MAC1D,CAAC,CAAC;;MAEF;MACAL,SAAS,CAACG,EAAE,CAAC,gBAAgB,EAAGK,WAAW,IAAK;QAC9CZ,WAAW,CAACU,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEE,WAAW,CAAC,CAAC;MAC7C,CAAC,CAAC;;MAEF;MACAR,SAAS,CAACG,EAAE,CAAC,QAAQ,EAAE,CAAC;QAAEM,QAAQ;QAAEC;MAAS,CAAC,KAAK;QACjDZ,cAAc,CAACQ,IAAI,KAAK;UACtB,GAAGA,IAAI;UACP,CAACG,QAAQ,GAAGC;QACd,CAAC,CAAC,CAAC;MACL,CAAC,CAAC;;MAEF;MACAV,SAAS,CAACG,EAAE,CAAC,kBAAkB,EAAE,CAAC;QAAEE,MAAM;QAAEM;MAAO,CAAC,KAAK;QACvD;QACAC,OAAO,CAACC,GAAG,CAAC,QAAQR,MAAM,sBAAsBM,MAAM,EAAE,CAAC;MAC3D,CAAC,CAAC;MAEFnB,SAAS,CAACQ,SAAS,CAAC;MAEpB,OAAO,MAAM;QACXA,SAAS,CAACc,KAAK,CAAC,CAAC;MACnB,CAAC;IACH;EACF,CAAC,EAAE,CAACf,IAAI,CAAC,CAAC;EAEV,MAAMgB,WAAW,GAAGA,CAACC,WAAW,EAAEC,OAAO,KAAK;IAC5C,IAAI1B,MAAM,IAAIQ,IAAI,EAAE;MAClBR,MAAM,CAACU,IAAI,CAAC,gBAAgB,EAAE;QAC5Be,WAAW;QACXC,OAAO;QACPR,QAAQ,EAAEV,IAAI,CAACG;MACjB,CAAC,CAAC;;MAEF;MACA,MAAMM,WAAW,GAAG;QAClBC,QAAQ,EAAEV,IAAI,CAACG,EAAE;QACjBe,OAAO;QACPC,SAAS,EAAE,IAAIC,IAAI,CAAC;MACtB,CAAC;MACDvB,WAAW,CAACU,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEE,WAAW,CAAC,CAAC;IAC7C;EACF,CAAC;EAED,MAAMY,mBAAmB,GAAGA,CAACJ,WAAW,EAAEN,QAAQ,KAAK;IACrD,IAAInB,MAAM,EAAE;MACVA,MAAM,CAACU,IAAI,CAAC,QAAQ,EAAE;QAAEe,WAAW;QAAEN;MAAS,CAAC,CAAC;IAClD;EACF,CAAC;EAED,MAAMW,YAAY,GAAIV,MAAM,IAAK;IAC/B,IAAIpB,MAAM,IAAIQ,IAAI,EAAE;MAClBR,MAAM,CAACU,IAAI,CAAC,cAAc,EAAE;QAAEI,MAAM,EAAEN,IAAI,CAACG,EAAE;QAAES;MAAO,CAAC,CAAC;IAC1D;EACF,CAAC;EAED,MAAMW,KAAK,GAAG;IACZ/B,MAAM;IACNE,WAAW;IACXE,QAAQ;IACRE,WAAW;IACXkB,WAAW;IACXK,mBAAmB;IACnBC,YAAY;IACZzB;EACF,CAAC;EAED,oBACEd,OAAA,CAACC,aAAa,CAACwC,QAAQ;IAACD,KAAK,EAAEA,KAAM;IAAAjC,QAAA,EAClCA;EAAQ;IAAAmC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACa,CAAC;AAE7B,CAAC;AAACrC,GAAA,CApGWF,cAAc;EAAA,QAKRR,OAAO;AAAA;AAAAgD,EAAA,GALbxC,cAAc;AAAA,IAAAwC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}