import React, { useState } from 'react';
import ChatList from './ChatList';
import ChatWindow from './ChatWindow';
import Header from '../Layout/Header';

const ChatDashboard = () => {
  const [selectedChat, setSelectedChat] = useState(null);

  const handleSelectChat = (user) => {
    setSelectedChat(user);
  };

  return (
    <div className="h-screen flex flex-col bg-whatsapp-gray">
      <Header />
      
      <div className="flex-1 flex flex-col md:flex-row overflow-hidden">
        <div className={`${selectedChat ? 'hidden md:flex' : 'flex'} md:w-1/3`}>
          <ChatList
            onSelectChat={handleSelectChat}
            selectedChat={selectedChat}
          />
        </div>
        <div className={`${selectedChat ? 'flex' : 'hidden md:flex'} flex-1`}>
          <ChatWindow
            selectedChat={selectedChat}
            onBack={() => setSelectedChat(null)}
          />
        </div>
      </div>
    </div>
  );
};

export default ChatDashboard;
