{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\yaaa\\\\client\\\\src\\\\components\\\\Auth\\\\Login.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { useAuth } from '../../contexts/AuthContext';\nimport { Link } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Login = () => {\n  _s();\n  const [formData, setFormData] = useState({\n    email: '',\n    password: ''\n  });\n  const [error, setError] = useState('');\n  const [loading, setLoading] = useState(false);\n  const {\n    login\n  } = useAuth();\n  const handleChange = e => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value\n    });\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setError('');\n    setLoading(true);\n    const result = await login(formData.email, formData.password);\n    if (!result.success) {\n      setError(result.message);\n    }\n    setLoading(false);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen flex items-center justify-center bg-whatsapp-gray\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-md w-full space-y-8 p-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"mt-6 text-3xl font-extrabold text-gray-900\",\n          children: \"Welcome to TinChat\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 39,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"mt-2 text-sm text-gray-600\",\n          children: \"Sign in to your account\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 42,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 38,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow-md p-6\",\n        children: /*#__PURE__*/_jsxDEV(\"form\", {\n          className: \"space-y-6\",\n          onSubmit: handleSubmit,\n          children: [error && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded\",\n            children: error\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 50,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"email\",\n              className: \"block text-sm font-medium text-gray-700\",\n              children: \"Email address\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 56,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              id: \"email\",\n              name: \"email\",\n              type: \"email\",\n              required: true,\n              className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-whatsapp-green focus:border-whatsapp-green\",\n              placeholder: \"Enter your email\",\n              value: formData.email,\n              onChange: handleChange\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 59,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 55,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"password\",\n              className: \"block text-sm font-medium text-gray-700\",\n              children: \"Password\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 72,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              id: \"password\",\n              name: \"password\",\n              type: \"password\",\n              required: true,\n              className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-whatsapp-green focus:border-whatsapp-green\",\n              placeholder: \"Enter your password\",\n              value: formData.password,\n              onChange: handleChange\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 75,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 71,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"submit\",\n              disabled: loading,\n              className: \"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-whatsapp-green hover:bg-whatsapp-green-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-whatsapp-green disabled:opacity-50\",\n              children: loading ? 'Signing in...' : 'Sign in'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 88,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 87,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm text-gray-600\",\n              children: [\"Don't have an account?\", ' ', /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/register\",\n                className: \"font-medium text-whatsapp-green hover:text-whatsapp-green-dark\",\n                children: \"Sign up\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 100,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 98,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 97,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 48,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 47,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 37,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 36,\n    columnNumber: 5\n  }, this);\n};\n_s(Login, \"bOhT3Ywp82T+XUB4vFWgTdIxB2E=\", false, function () {\n  return [useAuth];\n});\n_c = Login;\nexport default Login;\nvar _c;\n$RefreshReg$(_c, \"Login\");", "map": {"version": 3, "names": ["React", "useState", "useAuth", "Link", "jsxDEV", "_jsxDEV", "<PERSON><PERSON>", "_s", "formData", "setFormData", "email", "password", "error", "setError", "loading", "setLoading", "login", "handleChange", "e", "target", "name", "value", "handleSubmit", "preventDefault", "result", "success", "message", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSubmit", "htmlFor", "id", "type", "required", "placeholder", "onChange", "disabled", "to", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/yaaa/client/src/components/Auth/Login.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useAuth } from '../../contexts/AuthContext';\nimport { Link } from 'react-router-dom';\n\nconst Login = () => {\n  const [formData, setFormData] = useState({\n    email: '',\n    password: ''\n  });\n  const [error, setError] = useState('');\n  const [loading, setLoading] = useState(false);\n  const { login } = useAuth();\n\n  const handleChange = (e) => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value\n    });\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    setError('');\n    setLoading(true);\n\n    const result = await login(formData.email, formData.password);\n    \n    if (!result.success) {\n      setError(result.message);\n    }\n    \n    setLoading(false);\n  };\n\n  return (\n    <div className=\"min-h-screen flex items-center justify-center bg-whatsapp-gray\">\n      <div className=\"max-w-md w-full space-y-8 p-8\">\n        <div className=\"text-center\">\n          <h2 className=\"mt-6 text-3xl font-extrabold text-gray-900\">\n            Welcome to TinChat\n          </h2>\n          <p className=\"mt-2 text-sm text-gray-600\">\n            Sign in to your account\n          </p>\n        </div>\n        \n        <div className=\"bg-white rounded-lg shadow-md p-6\">\n          <form className=\"space-y-6\" onSubmit={handleSubmit}>\n            {error && (\n              <div className=\"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded\">\n                {error}\n              </div>\n            )}\n            \n            <div>\n              <label htmlFor=\"email\" className=\"block text-sm font-medium text-gray-700\">\n                Email address\n              </label>\n              <input\n                id=\"email\"\n                name=\"email\"\n                type=\"email\"\n                required\n                className=\"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-whatsapp-green focus:border-whatsapp-green\"\n                placeholder=\"Enter your email\"\n                value={formData.email}\n                onChange={handleChange}\n              />\n            </div>\n\n            <div>\n              <label htmlFor=\"password\" className=\"block text-sm font-medium text-gray-700\">\n                Password\n              </label>\n              <input\n                id=\"password\"\n                name=\"password\"\n                type=\"password\"\n                required\n                className=\"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-whatsapp-green focus:border-whatsapp-green\"\n                placeholder=\"Enter your password\"\n                value={formData.password}\n                onChange={handleChange}\n              />\n            </div>\n\n            <div>\n              <button\n                type=\"submit\"\n                disabled={loading}\n                className=\"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-whatsapp-green hover:bg-whatsapp-green-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-whatsapp-green disabled:opacity-50\"\n              >\n                {loading ? 'Signing in...' : 'Sign in'}\n              </button>\n            </div>\n\n            <div className=\"text-center\">\n              <span className=\"text-sm text-gray-600\">\n                Don't have an account?{' '}\n                <Link to=\"/register\" className=\"font-medium text-whatsapp-green hover:text-whatsapp-green-dark\">\n                  Sign up\n                </Link>\n              </span>\n            </div>\n          </form>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Login;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,OAAO,QAAQ,4BAA4B;AACpD,SAASC,IAAI,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,MAAMC,KAAK,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClB,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGR,QAAQ,CAAC;IACvCS,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE;EACZ,CAAC,CAAC;EACF,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGZ,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACa,OAAO,EAAEC,UAAU,CAAC,GAAGd,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM;IAAEe;EAAM,CAAC,GAAGd,OAAO,CAAC,CAAC;EAE3B,MAAMe,YAAY,GAAIC,CAAC,IAAK;IAC1BT,WAAW,CAAC;MACV,GAAGD,QAAQ;MACX,CAACU,CAAC,CAACC,MAAM,CAACC,IAAI,GAAGF,CAAC,CAACC,MAAM,CAACE;IAC5B,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,YAAY,GAAG,MAAOJ,CAAC,IAAK;IAChCA,CAAC,CAACK,cAAc,CAAC,CAAC;IAClBV,QAAQ,CAAC,EAAE,CAAC;IACZE,UAAU,CAAC,IAAI,CAAC;IAEhB,MAAMS,MAAM,GAAG,MAAMR,KAAK,CAACR,QAAQ,CAACE,KAAK,EAAEF,QAAQ,CAACG,QAAQ,CAAC;IAE7D,IAAI,CAACa,MAAM,CAACC,OAAO,EAAE;MACnBZ,QAAQ,CAACW,MAAM,CAACE,OAAO,CAAC;IAC1B;IAEAX,UAAU,CAAC,KAAK,CAAC;EACnB,CAAC;EAED,oBACEV,OAAA;IAAKsB,SAAS,EAAC,gEAAgE;IAAAC,QAAA,eAC7EvB,OAAA;MAAKsB,SAAS,EAAC,+BAA+B;MAAAC,QAAA,gBAC5CvB,OAAA;QAAKsB,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BvB,OAAA;UAAIsB,SAAS,EAAC,4CAA4C;UAAAC,QAAA,EAAC;QAE3D;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACL3B,OAAA;UAAGsB,SAAS,EAAC,4BAA4B;UAAAC,QAAA,EAAC;QAE1C;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAEN3B,OAAA;QAAKsB,SAAS,EAAC,mCAAmC;QAAAC,QAAA,eAChDvB,OAAA;UAAMsB,SAAS,EAAC,WAAW;UAACM,QAAQ,EAAEX,YAAa;UAAAM,QAAA,GAChDhB,KAAK,iBACJP,OAAA;YAAKsB,SAAS,EAAC,iEAAiE;YAAAC,QAAA,EAC7EhB;UAAK;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,eAED3B,OAAA;YAAAuB,QAAA,gBACEvB,OAAA;cAAO6B,OAAO,EAAC,OAAO;cAACP,SAAS,EAAC,yCAAyC;cAAAC,QAAA,EAAC;YAE3E;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR3B,OAAA;cACE8B,EAAE,EAAC,OAAO;cACVf,IAAI,EAAC,OAAO;cACZgB,IAAI,EAAC,OAAO;cACZC,QAAQ;cACRV,SAAS,EAAC,kJAAkJ;cAC5JW,WAAW,EAAC,kBAAkB;cAC9BjB,KAAK,EAAEb,QAAQ,CAACE,KAAM;cACtB6B,QAAQ,EAAEtB;YAAa;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAEN3B,OAAA;YAAAuB,QAAA,gBACEvB,OAAA;cAAO6B,OAAO,EAAC,UAAU;cAACP,SAAS,EAAC,yCAAyC;cAAAC,QAAA,EAAC;YAE9E;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR3B,OAAA;cACE8B,EAAE,EAAC,UAAU;cACbf,IAAI,EAAC,UAAU;cACfgB,IAAI,EAAC,UAAU;cACfC,QAAQ;cACRV,SAAS,EAAC,kJAAkJ;cAC5JW,WAAW,EAAC,qBAAqB;cACjCjB,KAAK,EAAEb,QAAQ,CAACG,QAAS;cACzB4B,QAAQ,EAAEtB;YAAa;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAEN3B,OAAA;YAAAuB,QAAA,eACEvB,OAAA;cACE+B,IAAI,EAAC,QAAQ;cACbI,QAAQ,EAAE1B,OAAQ;cAClBa,SAAS,EAAC,qQAAqQ;cAAAC,QAAA,EAE9Qd,OAAO,GAAG,eAAe,GAAG;YAAS;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAEN3B,OAAA;YAAKsB,SAAS,EAAC,aAAa;YAAAC,QAAA,eAC1BvB,OAAA;cAAMsB,SAAS,EAAC,uBAAuB;cAAAC,QAAA,GAAC,wBAChB,EAAC,GAAG,eAC1BvB,OAAA,CAACF,IAAI;gBAACsC,EAAE,EAAC,WAAW;gBAACd,SAAS,EAAC,gEAAgE;gBAAAC,QAAA,EAAC;cAEhG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACzB,EAAA,CAzGID,KAAK;EAAA,QAOSJ,OAAO;AAAA;AAAAwC,EAAA,GAPrBpC,KAAK;AA2GX,eAAeA,KAAK;AAAC,IAAAoC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}