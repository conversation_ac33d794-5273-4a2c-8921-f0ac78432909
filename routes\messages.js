const express = require('express');
const Message = require('../models/Message');
const auth = require('../middleware/auth');
const memoryStorage = require('../utils/memoryStorage');

const router = express.Router();

const isMongoConnected = () => {
  return require('mongoose').connection.readyState === 1;
};

// Get messages between two users
router.get('/:userId', auth, async (req, res) => {
  try {
    const { userId } = req.params;
    const currentUserId = req.user._id;

    if (isMongoConnected()) {
      const messages = await Message.find({
        $or: [
          { sender: currentUserId, recipient: userId },
          { sender: userId, recipient: currentUserId }
        ]
      })
      .populate('sender', 'username avatar')
      .populate('recipient', 'username avatar')
      .sort({ createdAt: 1 })
      .limit(100); // Limit to last 100 messages

      res.json(messages);
    } else {
      const messages = await memoryStorage.getMessages(currentUserId, userId);
      res.json(messages);
    }
  } catch (error) {
    console.error('Get messages error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Send a message
router.post('/', auth, async (req, res) => {
  try {
    const { recipientId, content, messageType = 'text' } = req.body;

    if (!content || !recipientId) {
      return res.status(400).json({ message: 'Content and recipient are required' });
    }

    if (isMongoConnected()) {
      const message = new Message({
        sender: req.user._id,
        recipient: recipientId,
        content,
        messageType
      });

      await message.save();

      // Populate sender and recipient info
      await message.populate('sender', 'username avatar');
      await message.populate('recipient', 'username avatar');

      res.status(201).json(message);
    } else {
      const message = await memoryStorage.createMessage({
        sender: req.user._id,
        recipient: recipientId,
        content,
        messageType
      });

      res.status(201).json(message);
    }
  } catch (error) {
    console.error('Send message error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Mark messages as read
router.put('/read/:userId', auth, async (req, res) => {
  try {
    const { userId } = req.params;
    const currentUserId = req.user._id;

    await Message.updateMany(
      {
        sender: userId,
        recipient: currentUserId,
        isRead: false
      },
      {
        isRead: true,
        readAt: new Date()
      }
    );

    res.json({ message: 'Messages marked as read' });
  } catch (error) {
    console.error('Mark messages read error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Get unread message count
router.get('/unread/count', auth, async (req, res) => {
  try {
    const unreadCount = await Message.countDocuments({
      recipient: req.user._id,
      isRead: false
    });

    res.json({ unreadCount });
  } catch (error) {
    console.error('Get unread count error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

module.exports = router;
