{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\yaaa\\\\client\\\\src\\\\components\\\\Chat\\\\MessageInput.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef } from 'react';\nimport { useSocket } from '../../contexts/SocketContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst MessageInput = ({\n  onSendMessage,\n  recipientId\n}) => {\n  _s();\n  const [message, setMessage] = useState('');\n  const [isTyping, setIsTyping] = useState(false);\n  const typingTimeoutRef = useRef(null);\n  const {\n    sendTypingIndicator\n  } = useSocket();\n  const handleInputChange = e => {\n    const value = e.target.value;\n    setMessage(value);\n\n    // Handle typing indicator\n    if (value.trim() && !isTyping) {\n      setIsTyping(true);\n      sendTypingIndicator(recipientId, true);\n    }\n\n    // Clear existing timeout\n    if (typingTimeoutRef.current) {\n      clearTimeout(typingTimeoutRef.current);\n    }\n\n    // Set new timeout to stop typing indicator\n    typingTimeoutRef.current = setTimeout(() => {\n      setIsTyping(false);\n      sendTypingIndicator(recipientId, false);\n    }, 1000);\n  };\n  const handleSubmit = e => {\n    e.preventDefault();\n    if (message.trim()) {\n      onSendMessage(message);\n      setMessage('');\n\n      // Stop typing indicator\n      if (isTyping) {\n        setIsTyping(false);\n        sendTypingIndicator(recipientId, false);\n      }\n\n      // Clear timeout\n      if (typingTimeoutRef.current) {\n        clearTimeout(typingTimeoutRef.current);\n      }\n    }\n  };\n  const handleKeyPress = e => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      handleSubmit(e);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"bg-white border-t border-gray-200 p-4\",\n    children: /*#__PURE__*/_jsxDEV(\"form\", {\n      onSubmit: handleSubmit,\n      className: \"flex items-center space-x-3\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex-1\",\n        children: /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          value: message,\n          onChange: handleInputChange,\n          onKeyPress: handleKeyPress,\n          placeholder: \"Type a message...\",\n          className: \"w-full px-4 py-2 border border-gray-300 rounded-full focus:outline-none focus:ring-2 focus:ring-whatsapp-green focus:border-transparent\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 63,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 62,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        type: \"submit\",\n        disabled: !message.trim(),\n        className: \"bg-whatsapp-green text-white p-2 rounded-full hover:bg-whatsapp-green-dark focus:outline-none focus:ring-2 focus:ring-whatsapp-green focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",\n        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n          className: \"w-5 h-5\",\n          fill: \"none\",\n          stroke: \"currentColor\",\n          viewBox: \"0 0 24 24\",\n          children: /*#__PURE__*/_jsxDEV(\"path\", {\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            strokeWidth: 2,\n            d: \"M12 19l9 2-9-18-9 18 9-2zm0 0v-8\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 84,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 78,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 73,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 61,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 60,\n    columnNumber: 5\n  }, this);\n};\n_s(MessageInput, \"Mg5c3LKgH1BEUnjrZncEuuLMT28=\", false, function () {\n  return [useSocket];\n});\n_c = MessageInput;\nexport default MessageInput;\nvar _c;\n$RefreshReg$(_c, \"MessageInput\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useSocket", "jsxDEV", "_jsxDEV", "MessageInput", "onSendMessage", "recipientId", "_s", "message", "setMessage", "isTyping", "setIsTyping", "typingTimeoutRef", "sendTypingIndicator", "handleInputChange", "e", "value", "target", "trim", "current", "clearTimeout", "setTimeout", "handleSubmit", "preventDefault", "handleKeyPress", "key", "shift<PERSON>ey", "className", "children", "onSubmit", "type", "onChange", "onKeyPress", "placeholder", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "disabled", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/yaaa/client/src/components/Chat/MessageInput.js"], "sourcesContent": ["import React, { useState, useRef } from 'react';\nimport { useSocket } from '../../contexts/SocketContext';\n\nconst MessageInput = ({ onSendMessage, recipientId }) => {\n  const [message, setMessage] = useState('');\n  const [isTyping, setIsTyping] = useState(false);\n  const typingTimeoutRef = useRef(null);\n  const { sendTypingIndicator } = useSocket();\n\n  const handleInputChange = (e) => {\n    const value = e.target.value;\n    setMessage(value);\n\n    // Handle typing indicator\n    if (value.trim() && !isTyping) {\n      setIsTyping(true);\n      sendTypingIndicator(recipientId, true);\n    }\n\n    // Clear existing timeout\n    if (typingTimeoutRef.current) {\n      clearTimeout(typingTimeoutRef.current);\n    }\n\n    // Set new timeout to stop typing indicator\n    typingTimeoutRef.current = setTimeout(() => {\n      setIsTyping(false);\n      sendTypingIndicator(recipientId, false);\n    }, 1000);\n  };\n\n  const handleSubmit = (e) => {\n    e.preventDefault();\n    \n    if (message.trim()) {\n      onSendMessage(message);\n      setMessage('');\n      \n      // Stop typing indicator\n      if (isTyping) {\n        setIsTyping(false);\n        sendTypingIndicator(recipientId, false);\n      }\n      \n      // Clear timeout\n      if (typingTimeoutRef.current) {\n        clearTimeout(typingTimeoutRef.current);\n      }\n    }\n  };\n\n  const handleKeyPress = (e) => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      handleSubmit(e);\n    }\n  };\n\n  return (\n    <div className=\"bg-white border-t border-gray-200 p-4\">\n      <form onSubmit={handleSubmit} className=\"flex items-center space-x-3\">\n        <div className=\"flex-1\">\n          <input\n            type=\"text\"\n            value={message}\n            onChange={handleInputChange}\n            onKeyPress={handleKeyPress}\n            placeholder=\"Type a message...\"\n            className=\"w-full px-4 py-2 border border-gray-300 rounded-full focus:outline-none focus:ring-2 focus:ring-whatsapp-green focus:border-transparent\"\n          />\n        </div>\n        \n        <button\n          type=\"submit\"\n          disabled={!message.trim()}\n          className=\"bg-whatsapp-green text-white p-2 rounded-full hover:bg-whatsapp-green-dark focus:outline-none focus:ring-2 focus:ring-whatsapp-green focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\"\n        >\n          <svg\n            className=\"w-5 h-5\"\n            fill=\"none\"\n            stroke=\"currentColor\"\n            viewBox=\"0 0 24 24\"\n          >\n            <path\n              strokeLinecap=\"round\"\n              strokeLinejoin=\"round\"\n              strokeWidth={2}\n              d=\"M12 19l9 2-9-18-9 18 9-2zm0 0v-8\"\n            />\n          </svg>\n        </button>\n      </form>\n    </div>\n  );\n};\n\nexport default MessageInput;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,QAAQ,OAAO;AAC/C,SAASC,SAAS,QAAQ,8BAA8B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzD,MAAMC,YAAY,GAAGA,CAAC;EAAEC,aAAa;EAAEC;AAAY,CAAC,KAAK;EAAAC,EAAA;EACvD,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGV,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACW,QAAQ,EAAEC,WAAW,CAAC,GAAGZ,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAMa,gBAAgB,GAAGZ,MAAM,CAAC,IAAI,CAAC;EACrC,MAAM;IAAEa;EAAoB,CAAC,GAAGZ,SAAS,CAAC,CAAC;EAE3C,MAAMa,iBAAiB,GAAIC,CAAC,IAAK;IAC/B,MAAMC,KAAK,GAAGD,CAAC,CAACE,MAAM,CAACD,KAAK;IAC5BP,UAAU,CAACO,KAAK,CAAC;;IAEjB;IACA,IAAIA,KAAK,CAACE,IAAI,CAAC,CAAC,IAAI,CAACR,QAAQ,EAAE;MAC7BC,WAAW,CAAC,IAAI,CAAC;MACjBE,mBAAmB,CAACP,WAAW,EAAE,IAAI,CAAC;IACxC;;IAEA;IACA,IAAIM,gBAAgB,CAACO,OAAO,EAAE;MAC5BC,YAAY,CAACR,gBAAgB,CAACO,OAAO,CAAC;IACxC;;IAEA;IACAP,gBAAgB,CAACO,OAAO,GAAGE,UAAU,CAAC,MAAM;MAC1CV,WAAW,CAAC,KAAK,CAAC;MAClBE,mBAAmB,CAACP,WAAW,EAAE,KAAK,CAAC;IACzC,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;EAED,MAAMgB,YAAY,GAAIP,CAAC,IAAK;IAC1BA,CAAC,CAACQ,cAAc,CAAC,CAAC;IAElB,IAAIf,OAAO,CAACU,IAAI,CAAC,CAAC,EAAE;MAClBb,aAAa,CAACG,OAAO,CAAC;MACtBC,UAAU,CAAC,EAAE,CAAC;;MAEd;MACA,IAAIC,QAAQ,EAAE;QACZC,WAAW,CAAC,KAAK,CAAC;QAClBE,mBAAmB,CAACP,WAAW,EAAE,KAAK,CAAC;MACzC;;MAEA;MACA,IAAIM,gBAAgB,CAACO,OAAO,EAAE;QAC5BC,YAAY,CAACR,gBAAgB,CAACO,OAAO,CAAC;MACxC;IACF;EACF,CAAC;EAED,MAAMK,cAAc,GAAIT,CAAC,IAAK;IAC5B,IAAIA,CAAC,CAACU,GAAG,KAAK,OAAO,IAAI,CAACV,CAAC,CAACW,QAAQ,EAAE;MACpCX,CAAC,CAACQ,cAAc,CAAC,CAAC;MAClBD,YAAY,CAACP,CAAC,CAAC;IACjB;EACF,CAAC;EAED,oBACEZ,OAAA;IAAKwB,SAAS,EAAC,uCAAuC;IAAAC,QAAA,eACpDzB,OAAA;MAAM0B,QAAQ,EAAEP,YAAa;MAACK,SAAS,EAAC,6BAA6B;MAAAC,QAAA,gBACnEzB,OAAA;QAAKwB,SAAS,EAAC,QAAQ;QAAAC,QAAA,eACrBzB,OAAA;UACE2B,IAAI,EAAC,MAAM;UACXd,KAAK,EAAER,OAAQ;UACfuB,QAAQ,EAAEjB,iBAAkB;UAC5BkB,UAAU,EAAER,cAAe;UAC3BS,WAAW,EAAC,mBAAmB;UAC/BN,SAAS,EAAC;QAAyI;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAENlC,OAAA;QACE2B,IAAI,EAAC,QAAQ;QACbQ,QAAQ,EAAE,CAAC9B,OAAO,CAACU,IAAI,CAAC,CAAE;QAC1BS,SAAS,EAAC,4NAA4N;QAAAC,QAAA,eAEtOzB,OAAA;UACEwB,SAAS,EAAC,SAAS;UACnBY,IAAI,EAAC,MAAM;UACXC,MAAM,EAAC,cAAc;UACrBC,OAAO,EAAC,WAAW;UAAAb,QAAA,eAEnBzB,OAAA;YACEuC,aAAa,EAAC,OAAO;YACrBC,cAAc,EAAC,OAAO;YACtBC,WAAW,EAAE,CAAE;YACfC,CAAC,EAAC;UAAkC;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAAC9B,EAAA,CA3FIH,YAAY;EAAA,QAIgBH,SAAS;AAAA;AAAA6C,EAAA,GAJrC1C,YAAY;AA6FlB,eAAeA,YAAY;AAAC,IAAA0C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}