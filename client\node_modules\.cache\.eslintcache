[{"C:\\Users\\<USER>\\OneDrive\\Desktop\\yaaa\\client\\src\\index.js": "1", "C:\\Users\\<USER>\\OneDrive\\Desktop\\yaaa\\client\\src\\reportWebVitals.js": "2", "C:\\Users\\<USER>\\OneDrive\\Desktop\\yaaa\\client\\src\\App.js": "3", "C:\\Users\\<USER>\\OneDrive\\Desktop\\yaaa\\client\\src\\contexts\\SocketContext.js": "4", "C:\\Users\\<USER>\\OneDrive\\Desktop\\yaaa\\client\\src\\contexts\\AuthContext.js": "5", "C:\\Users\\<USER>\\OneDrive\\Desktop\\yaaa\\client\\src\\components\\Auth\\Login.js": "6", "C:\\Users\\<USER>\\OneDrive\\Desktop\\yaaa\\client\\src\\components\\Auth\\Register.js": "7", "C:\\Users\\<USER>\\OneDrive\\Desktop\\yaaa\\client\\src\\components\\Chat\\ChatDashboard.js": "8", "C:\\Users\\<USER>\\OneDrive\\Desktop\\yaaa\\client\\src\\components\\Chat\\ChatWindow.js": "9", "C:\\Users\\<USER>\\OneDrive\\Desktop\\yaaa\\client\\src\\components\\Chat\\ChatList.js": "10", "C:\\Users\\<USER>\\OneDrive\\Desktop\\yaaa\\client\\src\\components\\Layout\\Header.js": "11", "C:\\Users\\<USER>\\OneDrive\\Desktop\\yaaa\\client\\src\\components\\Chat\\MessageInput.js": "12", "C:\\Users\\<USER>\\OneDrive\\Desktop\\yaaa\\client\\src\\components\\Chat\\Message.js": "13"}, {"size": 535, "mtime": 1753616688677, "results": "14", "hashOfConfig": "15"}, {"size": 362, "mtime": 1753616689093, "results": "16", "hashOfConfig": "15"}, {"size": 2454, "mtime": 1753618000355, "results": "17", "hashOfConfig": "15"}, {"size": 2887, "mtime": 1753617819226, "results": "18", "hashOfConfig": "15"}, {"size": 3273, "mtime": 1753617803473, "results": "19", "hashOfConfig": "15"}, {"size": 3720, "mtime": 1753617841715, "results": "20", "hashOfConfig": "15"}, {"size": 5347, "mtime": 1753617880867, "results": "21", "hashOfConfig": "15"}, {"size": 980, "mtime": 1753618266759, "results": "22", "hashOfConfig": "15"}, {"size": 5082, "mtime": 1753618293861, "results": "23", "hashOfConfig": "15"}, {"size": 3901, "mtime": 1753618252809, "results": "24", "hashOfConfig": "15"}, {"size": 4485, "mtime": 1753617983675, "results": "25", "hashOfConfig": "15"}, {"size": 2797, "mtime": 1753617953993, "results": "26", "hashOfConfig": "15"}, {"size": 948, "mtime": 1753617940311, "results": "27", "hashOfConfig": "15"}, {"filePath": "28", "messages": "29", "suppressedMessages": "30", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "psnj0l", {"filePath": "31", "messages": "32", "suppressedMessages": "33", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "37", "messages": "38", "suppressedMessages": "39", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\yaaa\\client\\src\\index.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\yaaa\\client\\src\\reportWebVitals.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\yaaa\\client\\src\\App.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\yaaa\\client\\src\\contexts\\SocketContext.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\yaaa\\client\\src\\contexts\\AuthContext.js", ["67"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\yaaa\\client\\src\\components\\Auth\\Login.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\yaaa\\client\\src\\components\\Auth\\Register.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\yaaa\\client\\src\\components\\Chat\\ChatDashboard.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\yaaa\\client\\src\\components\\Chat\\ChatWindow.js", ["68"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\yaaa\\client\\src\\components\\Chat\\ChatList.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\yaaa\\client\\src\\components\\Layout\\Header.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\yaaa\\client\\src\\components\\Chat\\MessageInput.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\yaaa\\client\\src\\components\\Chat\\Message.js", [], [], {"ruleId": "69", "severity": 1, "message": "70", "line": 106, "column": 13, "nodeType": "71", "messageId": "72", "endLine": 106, "endColumn": 21}, {"ruleId": "73", "severity": 1, "message": "74", "line": 29, "column": 6, "nodeType": "75", "endLine": 29, "endColumn": 20, "suggestions": "76"}, "no-unused-vars", "'response' is assigned a value but never used.", "Identifier", "unusedVar", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchMessages'. Either include it or remove the dependency array.", "ArrayExpression", ["77"], {"desc": "78", "fix": "79"}, "Update the dependencies array to be: [fetchMessages, selectedChat]", {"range": "80", "text": "81"}, [894, 908], "[fetchMessages, selectedChat]"]