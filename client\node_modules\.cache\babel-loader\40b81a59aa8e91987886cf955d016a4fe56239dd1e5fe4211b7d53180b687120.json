{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\yaaa\\\\client\\\\src\\\\components\\\\Chat\\\\ChatWindow.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport axios from 'axios';\nimport { useSocket } from '../../contexts/SocketContext';\nimport { useAuth } from '../../contexts/AuthContext';\nimport MessageInput from './MessageInput';\nimport Message from './Message';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ChatWindow = ({\n  selectedChat,\n  onBack\n}) => {\n  _s();\n  const [messages, setMessages] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const messagesEndRef = useRef(null);\n  const {\n    user\n  } = useAuth();\n  const {\n    messages: socketMessages,\n    typingUsers\n  } = useSocket();\n\n  // Scroll to bottom when new messages arrive\n  const scrollToBottom = () => {\n    var _messagesEndRef$curre;\n    (_messagesEndRef$curre = messagesEndRef.current) === null || _messagesEndRef$curre === void 0 ? void 0 : _messagesEndRef$curre.scrollIntoView({\n      behavior: 'smooth'\n    });\n  };\n  useEffect(() => {\n    scrollToBottom();\n  }, [messages]);\n\n  // Fetch messages when chat is selected\n  useEffect(() => {\n    if (selectedChat) {\n      fetchMessages();\n    }\n  }, [selectedChat]);\n\n  // Listen for new socket messages\n  useEffect(() => {\n    if (selectedChat && socketMessages.length > 0) {\n      const newMessage = socketMessages[socketMessages.length - 1];\n      if (newMessage.senderId === selectedChat._id || newMessage.senderId === user.id) {\n        setMessages(prev => {\n          // Avoid duplicates\n          const exists = prev.some(msg => msg.createdAt === newMessage.timestamp && msg.content === newMessage.message);\n          if (!exists) {\n            return [...prev, {\n              _id: Date.now(),\n              sender: {\n                _id: newMessage.senderId\n              },\n              content: newMessage.message,\n              createdAt: newMessage.timestamp\n            }];\n          }\n          return prev;\n        });\n      }\n    }\n  }, [socketMessages, selectedChat, user.id]);\n  const fetchMessages = async () => {\n    if (!selectedChat) return;\n    setLoading(true);\n    try {\n      const response = await axios.get(`http://localhost:5000/api/messages/${selectedChat._id}`);\n      setMessages(response.data);\n\n      // Mark messages as read\n      await axios.put(`http://localhost:5000/api/messages/read/${selectedChat._id}`);\n    } catch (error) {\n      console.error('Error fetching messages:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleSendMessage = async content => {\n    if (!selectedChat || !content.trim()) return;\n    try {\n      // Send via API for persistence\n      const response = await axios.post('http://localhost:5000/api/messages', {\n        recipientId: selectedChat._id,\n        content: content.trim()\n      });\n\n      // Add to local state\n      setMessages(prev => [...prev, response.data]);\n    } catch (error) {\n      console.error('Error sending message:', error);\n    }\n  };\n  if (!selectedChat) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 flex items-center justify-center bg-whatsapp-chat-bg\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center text-gray-500\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-6xl mb-4\",\n          children: \"\\uD83D\\uDCAC\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 94,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-xl font-medium mb-2\",\n          children: \"Welcome to TinChat\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 95,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Select a contact to start chatting\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 96,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 93,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 92,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex-1 flex flex-col bg-whatsapp-chat-bg\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white border-b border-gray-200 p-4 flex items-center space-x-3\",\n      children: [onBack && /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: onBack,\n        className: \"md:hidden p-2 hover:bg-gray-100 rounded-full\",\n        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n          className: \"w-5 h-5\",\n          fill: \"none\",\n          stroke: \"currentColor\",\n          viewBox: \"0 0 24 24\",\n          children: /*#__PURE__*/_jsxDEV(\"path\", {\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            strokeWidth: 2,\n            d: \"M15 19l-7-7 7-7\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 112,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 111,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 107,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"w-10 h-10 bg-whatsapp-green rounded-full flex items-center justify-center text-white font-semibold\",\n        children: selectedChat.avatar || selectedChat.username.charAt(0).toUpperCase()\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 116,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex-1\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"font-medium text-gray-900\",\n          children: selectedChat.username\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 120,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-sm text-gray-500\",\n          children: typingUsers[selectedChat._id] ? 'typing...' : selectedChat.status\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 121,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 119,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 105,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 overflow-y-auto p-4 space-y-4\",\n      children: [loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-center\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-gray-500\",\n          children: \"Loading messages...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 131,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 130,\n        columnNumber: 11\n      }, this) : messages.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center text-gray-500\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"No messages yet. Start the conversation!\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 135,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 134,\n        columnNumber: 11\n      }, this) : messages.map(message => /*#__PURE__*/_jsxDEV(Message, {\n        message: message,\n        isOwn: message.sender._id === user.id\n      }, message._id, false, {\n        fileName: _jsxFileName,\n        lineNumber: 139,\n        columnNumber: 13\n      }, this)), /*#__PURE__*/_jsxDEV(\"div\", {\n        ref: messagesEndRef\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 146,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 128,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(MessageInput, {\n      onSendMessage: handleSendMessage,\n      recipientId: selectedChat._id\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 150,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 103,\n    columnNumber: 5\n  }, this);\n};\n_s(ChatWindow, \"WHELSQEQQJRIgnwv2XehRsSTFBk=\", false, function () {\n  return [useAuth, useSocket];\n});\n_c = ChatWindow;\nexport default ChatWindow;\nvar _c;\n$RefreshReg$(_c, \"ChatWindow\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "axios", "useSocket", "useAuth", "MessageInput", "Message", "jsxDEV", "_jsxDEV", "ChatWindow", "selectedC<PERSON>", "onBack", "_s", "messages", "setMessages", "loading", "setLoading", "messagesEndRef", "user", "socketMessages", "typingUsers", "scrollToBottom", "_messagesEndRef$curre", "current", "scrollIntoView", "behavior", "fetchMessages", "length", "newMessage", "senderId", "_id", "id", "prev", "exists", "some", "msg", "createdAt", "timestamp", "content", "message", "Date", "now", "sender", "response", "get", "data", "put", "error", "console", "handleSendMessage", "trim", "post", "recipientId", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "avatar", "username", "char<PERSON>t", "toUpperCase", "status", "map", "isOwn", "ref", "onSendMessage", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/yaaa/client/src/components/Chat/ChatWindow.js"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport axios from 'axios';\nimport { useSocket } from '../../contexts/SocketContext';\nimport { useAuth } from '../../contexts/AuthContext';\nimport MessageInput from './MessageInput';\nimport Message from './Message';\n\nconst ChatWindow = ({ selectedChat, onBack }) => {\n  const [messages, setMessages] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const messagesEndRef = useRef(null);\n  const { user } = useAuth();\n  const { messages: socketMessages, typingUsers } = useSocket();\n\n  // Scroll to bottom when new messages arrive\n  const scrollToBottom = () => {\n    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });\n  };\n\n  useEffect(() => {\n    scrollToBottom();\n  }, [messages]);\n\n  // Fetch messages when chat is selected\n  useEffect(() => {\n    if (selectedChat) {\n      fetchMessages();\n    }\n  }, [selectedChat]);\n\n  // Listen for new socket messages\n  useEffect(() => {\n    if (selectedChat && socketMessages.length > 0) {\n      const newMessage = socketMessages[socketMessages.length - 1];\n      if (newMessage.senderId === selectedChat._id || newMessage.senderId === user.id) {\n        setMessages(prev => {\n          // Avoid duplicates\n          const exists = prev.some(msg => \n            msg.createdAt === newMessage.timestamp && \n            msg.content === newMessage.message\n          );\n          if (!exists) {\n            return [...prev, {\n              _id: Date.now(),\n              sender: { _id: newMessage.senderId },\n              content: newMessage.message,\n              createdAt: newMessage.timestamp\n            }];\n          }\n          return prev;\n        });\n      }\n    }\n  }, [socketMessages, selectedChat, user.id]);\n\n  const fetchMessages = async () => {\n    if (!selectedChat) return;\n    \n    setLoading(true);\n    try {\n      const response = await axios.get(`http://localhost:5000/api/messages/${selectedChat._id}`);\n      setMessages(response.data);\n      \n      // Mark messages as read\n      await axios.put(`http://localhost:5000/api/messages/read/${selectedChat._id}`);\n    } catch (error) {\n      console.error('Error fetching messages:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleSendMessage = async (content) => {\n    if (!selectedChat || !content.trim()) return;\n\n    try {\n      // Send via API for persistence\n      const response = await axios.post('http://localhost:5000/api/messages', {\n        recipientId: selectedChat._id,\n        content: content.trim()\n      });\n\n      // Add to local state\n      setMessages(prev => [...prev, response.data]);\n    } catch (error) {\n      console.error('Error sending message:', error);\n    }\n  };\n\n  if (!selectedChat) {\n    return (\n      <div className=\"flex-1 flex items-center justify-center bg-whatsapp-chat-bg\">\n        <div className=\"text-center text-gray-500\">\n          <div className=\"text-6xl mb-4\">💬</div>\n          <h3 className=\"text-xl font-medium mb-2\">Welcome to TinChat</h3>\n          <p>Select a contact to start chatting</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"flex-1 flex flex-col bg-whatsapp-chat-bg\">\n      {/* Chat Header */}\n      <div className=\"bg-white border-b border-gray-200 p-4 flex items-center space-x-3\">\n        {onBack && (\n          <button\n            onClick={onBack}\n            className=\"md:hidden p-2 hover:bg-gray-100 rounded-full\"\n          >\n            <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 19l-7-7 7-7\" />\n            </svg>\n          </button>\n        )}\n        <div className=\"w-10 h-10 bg-whatsapp-green rounded-full flex items-center justify-center text-white font-semibold\">\n          {selectedChat.avatar || selectedChat.username.charAt(0).toUpperCase()}\n        </div>\n        <div className=\"flex-1\">\n          <h3 className=\"font-medium text-gray-900\">{selectedChat.username}</h3>\n          <p className=\"text-sm text-gray-500\">\n            {typingUsers[selectedChat._id] ? 'typing...' : selectedChat.status}\n          </p>\n        </div>\n      </div>\n\n      {/* Messages Area */}\n      <div className=\"flex-1 overflow-y-auto p-4 space-y-4\">\n        {loading ? (\n          <div className=\"flex justify-center\">\n            <div className=\"text-gray-500\">Loading messages...</div>\n          </div>\n        ) : messages.length === 0 ? (\n          <div className=\"text-center text-gray-500\">\n            <p>No messages yet. Start the conversation!</p>\n          </div>\n        ) : (\n          messages.map((message) => (\n            <Message\n              key={message._id}\n              message={message}\n              isOwn={message.sender._id === user.id}\n            />\n          ))\n        )}\n        <div ref={messagesEndRef} />\n      </div>\n\n      {/* Message Input */}\n      <MessageInput\n        onSendMessage={handleSendMessage}\n        recipientId={selectedChat._id}\n      />\n    </div>\n  );\n};\n\nexport default ChatWindow;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,SAAS,QAAQ,8BAA8B;AACxD,SAASC,OAAO,QAAQ,4BAA4B;AACpD,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAOC,OAAO,MAAM,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhC,MAAMC,UAAU,GAAGA,CAAC;EAAEC,YAAY;EAAEC;AAAO,CAAC,KAAK;EAAAC,EAAA;EAC/C,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGf,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACgB,OAAO,EAAEC,UAAU,CAAC,GAAGjB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAMkB,cAAc,GAAGhB,MAAM,CAAC,IAAI,CAAC;EACnC,MAAM;IAAEiB;EAAK,CAAC,GAAGd,OAAO,CAAC,CAAC;EAC1B,MAAM;IAAES,QAAQ,EAAEM,cAAc;IAAEC;EAAY,CAAC,GAAGjB,SAAS,CAAC,CAAC;;EAE7D;EACA,MAAMkB,cAAc,GAAGA,CAAA,KAAM;IAAA,IAAAC,qBAAA;IAC3B,CAAAA,qBAAA,GAAAL,cAAc,CAACM,OAAO,cAAAD,qBAAA,uBAAtBA,qBAAA,CAAwBE,cAAc,CAAC;MAAEC,QAAQ,EAAE;IAAS,CAAC,CAAC;EAChE,CAAC;EAEDzB,SAAS,CAAC,MAAM;IACdqB,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,CAACR,QAAQ,CAAC,CAAC;;EAEd;EACAb,SAAS,CAAC,MAAM;IACd,IAAIU,YAAY,EAAE;MAChBgB,aAAa,CAAC,CAAC;IACjB;EACF,CAAC,EAAE,CAAChB,YAAY,CAAC,CAAC;;EAElB;EACAV,SAAS,CAAC,MAAM;IACd,IAAIU,YAAY,IAAIS,cAAc,CAACQ,MAAM,GAAG,CAAC,EAAE;MAC7C,MAAMC,UAAU,GAAGT,cAAc,CAACA,cAAc,CAACQ,MAAM,GAAG,CAAC,CAAC;MAC5D,IAAIC,UAAU,CAACC,QAAQ,KAAKnB,YAAY,CAACoB,GAAG,IAAIF,UAAU,CAACC,QAAQ,KAAKX,IAAI,CAACa,EAAE,EAAE;QAC/EjB,WAAW,CAACkB,IAAI,IAAI;UAClB;UACA,MAAMC,MAAM,GAAGD,IAAI,CAACE,IAAI,CAACC,GAAG,IAC1BA,GAAG,CAACC,SAAS,KAAKR,UAAU,CAACS,SAAS,IACtCF,GAAG,CAACG,OAAO,KAAKV,UAAU,CAACW,OAC7B,CAAC;UACD,IAAI,CAACN,MAAM,EAAE;YACX,OAAO,CAAC,GAAGD,IAAI,EAAE;cACfF,GAAG,EAAEU,IAAI,CAACC,GAAG,CAAC,CAAC;cACfC,MAAM,EAAE;gBAAEZ,GAAG,EAAEF,UAAU,CAACC;cAAS,CAAC;cACpCS,OAAO,EAAEV,UAAU,CAACW,OAAO;cAC3BH,SAAS,EAAER,UAAU,CAACS;YACxB,CAAC,CAAC;UACJ;UACA,OAAOL,IAAI;QACb,CAAC,CAAC;MACJ;IACF;EACF,CAAC,EAAE,CAACb,cAAc,EAAET,YAAY,EAAEQ,IAAI,CAACa,EAAE,CAAC,CAAC;EAE3C,MAAML,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI,CAAChB,YAAY,EAAE;IAEnBM,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAM2B,QAAQ,GAAG,MAAMzC,KAAK,CAAC0C,GAAG,CAAC,sCAAsClC,YAAY,CAACoB,GAAG,EAAE,CAAC;MAC1FhB,WAAW,CAAC6B,QAAQ,CAACE,IAAI,CAAC;;MAE1B;MACA,MAAM3C,KAAK,CAAC4C,GAAG,CAAC,2CAA2CpC,YAAY,CAACoB,GAAG,EAAE,CAAC;IAChF,CAAC,CAAC,OAAOiB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;IAClD,CAAC,SAAS;MACR/B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMiC,iBAAiB,GAAG,MAAOX,OAAO,IAAK;IAC3C,IAAI,CAAC5B,YAAY,IAAI,CAAC4B,OAAO,CAACY,IAAI,CAAC,CAAC,EAAE;IAEtC,IAAI;MACF;MACA,MAAMP,QAAQ,GAAG,MAAMzC,KAAK,CAACiD,IAAI,CAAC,oCAAoC,EAAE;QACtEC,WAAW,EAAE1C,YAAY,CAACoB,GAAG;QAC7BQ,OAAO,EAAEA,OAAO,CAACY,IAAI,CAAC;MACxB,CAAC,CAAC;;MAEF;MACApC,WAAW,CAACkB,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEW,QAAQ,CAACE,IAAI,CAAC,CAAC;IAC/C,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;IAChD;EACF,CAAC;EAED,IAAI,CAACrC,YAAY,EAAE;IACjB,oBACEF,OAAA;MAAK6C,SAAS,EAAC,6DAA6D;MAAAC,QAAA,eAC1E9C,OAAA;QAAK6C,SAAS,EAAC,2BAA2B;QAAAC,QAAA,gBACxC9C,OAAA;UAAK6C,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACvClD,OAAA;UAAI6C,SAAS,EAAC,0BAA0B;UAAAC,QAAA,EAAC;QAAkB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAChElD,OAAA;UAAA8C,QAAA,EAAG;QAAkC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACElD,OAAA;IAAK6C,SAAS,EAAC,0CAA0C;IAAAC,QAAA,gBAEvD9C,OAAA;MAAK6C,SAAS,EAAC,mEAAmE;MAAAC,QAAA,GAC/E3C,MAAM,iBACLH,OAAA;QACEmD,OAAO,EAAEhD,MAAO;QAChB0C,SAAS,EAAC,8CAA8C;QAAAC,QAAA,eAExD9C,OAAA;UAAK6C,SAAS,EAAC,SAAS;UAACO,IAAI,EAAC,MAAM;UAACC,MAAM,EAAC,cAAc;UAACC,OAAO,EAAC,WAAW;UAAAR,QAAA,eAC5E9C,OAAA;YAAMuD,aAAa,EAAC,OAAO;YAACC,cAAc,EAAC,OAAO;YAACC,WAAW,EAAE,CAAE;YAACC,CAAC,EAAC;UAAiB;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CACT,eACDlD,OAAA;QAAK6C,SAAS,EAAC,oGAAoG;QAAAC,QAAA,EAChH5C,YAAY,CAACyD,MAAM,IAAIzD,YAAY,CAAC0D,QAAQ,CAACC,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC;MAAC;QAAAf,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClE,CAAC,eACNlD,OAAA;QAAK6C,SAAS,EAAC,QAAQ;QAAAC,QAAA,gBACrB9C,OAAA;UAAI6C,SAAS,EAAC,2BAA2B;UAAAC,QAAA,EAAE5C,YAAY,CAAC0D;QAAQ;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACtElD,OAAA;UAAG6C,SAAS,EAAC,uBAAuB;UAAAC,QAAA,EACjClC,WAAW,CAACV,YAAY,CAACoB,GAAG,CAAC,GAAG,WAAW,GAAGpB,YAAY,CAAC6D;QAAM;UAAAhB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNlD,OAAA;MAAK6C,SAAS,EAAC,sCAAsC;MAAAC,QAAA,GAClDvC,OAAO,gBACNP,OAAA;QAAK6C,SAAS,EAAC,qBAAqB;QAAAC,QAAA,eAClC9C,OAAA;UAAK6C,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAmB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrD,CAAC,GACJ7C,QAAQ,CAACc,MAAM,KAAK,CAAC,gBACvBnB,OAAA;QAAK6C,SAAS,EAAC,2BAA2B;QAAAC,QAAA,eACxC9C,OAAA;UAAA8C,QAAA,EAAG;QAAwC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5C,CAAC,GAEN7C,QAAQ,CAAC2D,GAAG,CAAEjC,OAAO,iBACnB/B,OAAA,CAACF,OAAO;QAENiC,OAAO,EAAEA,OAAQ;QACjBkC,KAAK,EAAElC,OAAO,CAACG,MAAM,CAACZ,GAAG,KAAKZ,IAAI,CAACa;MAAG,GAFjCQ,OAAO,CAACT,GAAG;QAAAyB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAGjB,CACF,CACF,eACDlD,OAAA;QAAKkE,GAAG,EAAEzD;MAAe;QAAAsC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzB,CAAC,eAGNlD,OAAA,CAACH,YAAY;MACXsE,aAAa,EAAE1B,iBAAkB;MACjCG,WAAW,EAAE1C,YAAY,CAACoB;IAAI;MAAAyB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/B,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAAC9C,EAAA,CApJIH,UAAU;EAAA,QAIGL,OAAO,EAC0BD,SAAS;AAAA;AAAAyE,EAAA,GALvDnE,UAAU;AAsJhB,eAAeA,UAAU;AAAC,IAAAmE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}