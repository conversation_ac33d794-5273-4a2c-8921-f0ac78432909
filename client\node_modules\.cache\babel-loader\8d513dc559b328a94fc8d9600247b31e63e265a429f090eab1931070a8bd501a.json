{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\yaaa\\\\client\\\\src\\\\components\\\\Chat\\\\Message.js\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Message = ({\n  message,\n  isOwn\n}) => {\n  const formatTime = timestamp => {\n    const date = new Date(timestamp);\n    return date.toLocaleTimeString([], {\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `flex ${isOwn ? 'justify-end' : 'justify-start'}`,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${isOwn ? 'bg-whatsapp-green text-white rounded-br-none' : 'bg-white text-gray-800 rounded-bl-none shadow-sm'}`,\n      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-sm\",\n        children: message.content\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 18,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `text-xs mt-1 ${isOwn ? 'text-green-100' : 'text-gray-500'}`,\n        children: [formatTime(message.createdAt), isOwn && /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"ml-1\",\n          children: message.isRead ? '✓✓' : '✓'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 22,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 19,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 11,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 10,\n    columnNumber: 5\n  }, this);\n};\n_c = Message;\nexport default Message;\nvar _c;\n$RefreshReg$(_c, \"Message\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "Message", "message", "isOwn", "formatTime", "timestamp", "date", "Date", "toLocaleTimeString", "hour", "minute", "className", "children", "content", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "createdAt", "isRead", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/yaaa/client/src/components/Chat/Message.js"], "sourcesContent": ["import React from 'react';\n\nconst Message = ({ message, isOwn }) => {\n  const formatTime = (timestamp) => {\n    const date = new Date(timestamp);\n    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });\n  };\n\n  return (\n    <div className={`flex ${isOwn ? 'justify-end' : 'justify-start'}`}>\n      <div\n        className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${\n          isOwn\n            ? 'bg-whatsapp-green text-white rounded-br-none'\n            : 'bg-white text-gray-800 rounded-bl-none shadow-sm'\n        }`}\n      >\n        <p className=\"text-sm\">{message.content}</p>\n        <div className={`text-xs mt-1 ${isOwn ? 'text-green-100' : 'text-gray-500'}`}>\n          {formatTime(message.createdAt)}\n          {isOwn && (\n            <span className=\"ml-1\">\n              {message.isRead ? '✓✓' : '✓'}\n            </span>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Message;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,OAAO,GAAGA,CAAC;EAAEC,OAAO;EAAEC;AAAM,CAAC,KAAK;EACtC,MAAMC,UAAU,GAAIC,SAAS,IAAK;IAChC,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,SAAS,CAAC;IAChC,OAAOC,IAAI,CAACE,kBAAkB,CAAC,EAAE,EAAE;MAAEC,IAAI,EAAE,SAAS;MAAEC,MAAM,EAAE;IAAU,CAAC,CAAC;EAC5E,CAAC;EAED,oBACEV,OAAA;IAAKW,SAAS,EAAE,QAAQR,KAAK,GAAG,aAAa,GAAG,eAAe,EAAG;IAAAS,QAAA,eAChEZ,OAAA;MACEW,SAAS,EAAE,6CACTR,KAAK,GACD,8CAA8C,GAC9C,kDAAkD,EACrD;MAAAS,QAAA,gBAEHZ,OAAA;QAAGW,SAAS,EAAC,SAAS;QAAAC,QAAA,EAAEV,OAAO,CAACW;MAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC5CjB,OAAA;QAAKW,SAAS,EAAE,gBAAgBR,KAAK,GAAG,gBAAgB,GAAG,eAAe,EAAG;QAAAS,QAAA,GAC1ER,UAAU,CAACF,OAAO,CAACgB,SAAS,CAAC,EAC7Bf,KAAK,iBACJH,OAAA;UAAMW,SAAS,EAAC,MAAM;UAAAC,QAAA,EACnBV,OAAO,CAACiB,MAAM,GAAG,IAAI,GAAG;QAAG;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB,CACP;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACG,EAAA,GA3BInB,OAAO;AA6Bb,eAAeA,OAAO;AAAC,IAAAmB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}