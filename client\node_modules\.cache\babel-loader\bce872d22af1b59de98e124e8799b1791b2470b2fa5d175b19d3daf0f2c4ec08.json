{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\yaaa\\\\client\\\\src\\\\components\\\\Chat\\\\ChatList.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport axios from 'axios';\nimport { useSocket } from '../../contexts/SocketContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ChatList = ({\n  onSelectChat,\n  selectedChat\n}) => {\n  _s();\n  const [users, setUsers] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const {\n    onlineUsers\n  } = useSocket();\n  useEffect(() => {\n    const fetchUsers = async () => {\n      try {\n        const response = await axios.get('http://localhost:5000/api/users');\n        setUsers(response.data);\n      } catch (error) {\n        console.error('Error fetching users:', error);\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchUsers();\n  }, []);\n  const isUserOnline = userId => {\n    return onlineUsers.includes(userId);\n  };\n  const getStatusColor = status => {\n    switch (status) {\n      case 'Available':\n        return 'bg-green-500';\n      case 'Busy':\n        return 'bg-red-500';\n      case 'Away':\n        return 'bg-yellow-500';\n      case 'Do not disturb':\n        return 'bg-gray-500';\n      default:\n        return 'bg-gray-400';\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"w-1/3 bg-white border-r border-gray-200 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-gray-500\",\n        children: \"Loading contacts...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 47,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 46,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"w-full md:w-1/3 bg-white border-r border-gray-200 flex flex-col\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-4 border-b border-gray-200\",\n      children: /*#__PURE__*/_jsxDEV(\"h2\", {\n        className: \"text-xl font-semibold text-gray-800\",\n        children: \"Chats\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 55,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 54,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 overflow-y-auto\",\n      children: users.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-4 text-center text-gray-500\",\n        children: \"No contacts available\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 60,\n        columnNumber: 11\n      }, this) : users.map(user => /*#__PURE__*/_jsxDEV(\"div\", {\n        onClick: () => onSelectChat(user),\n        className: `p-4 border-b border-gray-100 cursor-pointer hover:bg-gray-50 transition-colors ${(selectedChat === null || selectedChat === void 0 ? void 0 : selectedChat._id) === user._id ? 'bg-whatsapp-green-light' : ''}`,\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-12 h-12 bg-whatsapp-green rounded-full flex items-center justify-center text-white font-semibold\",\n              children: user.avatar || user.username.charAt(0).toUpperCase()\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 74,\n              columnNumber: 19\n            }, this), isUserOnline(user._id) && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute -bottom-1 -right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-white\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 78,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 73,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1 min-w-0\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-sm font-medium text-gray-900 truncate\",\n                children: user.username\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 84,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-1\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `w-2 h-2 rounded-full ${getStatusColor(user.status)}`\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 88,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 87,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 83,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xs text-gray-500 truncate\",\n                children: user.status\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 93,\n                columnNumber: 21\n              }, this), isUserOnline(user._id) ? /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-xs text-green-600\",\n                children: \"Online\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 97,\n                columnNumber: 23\n              }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-xs text-gray-400\",\n                children: user.lastSeen ? new Date(user.lastSeen).toLocaleDateString() : 'Offline'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 99,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 92,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 82,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 72,\n          columnNumber: 15\n        }, this)\n      }, user._id, false, {\n        fileName: _jsxFileName,\n        lineNumber: 65,\n        columnNumber: 13\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 58,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 53,\n    columnNumber: 5\n  }, this);\n};\n_s(ChatList, \"SE9sTWFwjnig9vN42chB0T9A1ec=\", false, function () {\n  return [useSocket];\n});\n_c = ChatList;\nexport default ChatList;\nvar _c;\n$RefreshReg$(_c, \"ChatList\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "axios", "useSocket", "jsxDEV", "_jsxDEV", "ChatList", "onSelectChat", "selectedC<PERSON>", "_s", "users", "setUsers", "loading", "setLoading", "onlineUsers", "fetchUsers", "response", "get", "data", "error", "console", "isUserOnline", "userId", "includes", "getStatusColor", "status", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "length", "map", "user", "onClick", "_id", "avatar", "username", "char<PERSON>t", "toUpperCase", "lastSeen", "Date", "toLocaleDateString", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/yaaa/client/src/components/Chat/ChatList.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport axios from 'axios';\nimport { useSocket } from '../../contexts/SocketContext';\n\nconst ChatList = ({ onSelectChat, selectedChat }) => {\n  const [users, setUsers] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const { onlineUsers } = useSocket();\n\n  useEffect(() => {\n    const fetchUsers = async () => {\n      try {\n        const response = await axios.get('http://localhost:5000/api/users');\n        setUsers(response.data);\n      } catch (error) {\n        console.error('Error fetching users:', error);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchUsers();\n  }, []);\n\n  const isUserOnline = (userId) => {\n    return onlineUsers.includes(userId);\n  };\n\n  const getStatusColor = (status) => {\n    switch (status) {\n      case 'Available':\n        return 'bg-green-500';\n      case 'Busy':\n        return 'bg-red-500';\n      case 'Away':\n        return 'bg-yellow-500';\n      case 'Do not disturb':\n        return 'bg-gray-500';\n      default:\n        return 'bg-gray-400';\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"w-1/3 bg-white border-r border-gray-200 flex items-center justify-center\">\n        <div className=\"text-gray-500\">Loading contacts...</div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"w-full md:w-1/3 bg-white border-r border-gray-200 flex flex-col\">\n      <div className=\"p-4 border-b border-gray-200\">\n        <h2 className=\"text-xl font-semibold text-gray-800\">Chats</h2>\n      </div>\n      \n      <div className=\"flex-1 overflow-y-auto\">\n        {users.length === 0 ? (\n          <div className=\"p-4 text-center text-gray-500\">\n            No contacts available\n          </div>\n        ) : (\n          users.map((user) => (\n            <div\n              key={user._id}\n              onClick={() => onSelectChat(user)}\n              className={`p-4 border-b border-gray-100 cursor-pointer hover:bg-gray-50 transition-colors ${\n                selectedChat?._id === user._id ? 'bg-whatsapp-green-light' : ''\n              }`}\n            >\n              <div className=\"flex items-center space-x-3\">\n                <div className=\"relative\">\n                  <div className=\"w-12 h-12 bg-whatsapp-green rounded-full flex items-center justify-center text-white font-semibold\">\n                    {user.avatar || user.username.charAt(0).toUpperCase()}\n                  </div>\n                  {isUserOnline(user._id) && (\n                    <div className=\"absolute -bottom-1 -right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-white\"></div>\n                  )}\n                </div>\n                \n                <div className=\"flex-1 min-w-0\">\n                  <div className=\"flex items-center justify-between\">\n                    <h3 className=\"text-sm font-medium text-gray-900 truncate\">\n                      {user.username}\n                    </h3>\n                    <div className=\"flex items-center space-x-1\">\n                      <div className={`w-2 h-2 rounded-full ${getStatusColor(user.status)}`}></div>\n                    </div>\n                  </div>\n                  \n                  <div className=\"flex items-center justify-between\">\n                    <p className=\"text-xs text-gray-500 truncate\">\n                      {user.status}\n                    </p>\n                    {isUserOnline(user._id) ? (\n                      <span className=\"text-xs text-green-600\">Online</span>\n                    ) : (\n                      <span className=\"text-xs text-gray-400\">\n                        {user.lastSeen ? new Date(user.lastSeen).toLocaleDateString() : 'Offline'}\n                      </span>\n                    )}\n                  </div>\n                </div>\n              </div>\n            </div>\n          ))\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default ChatList;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,SAAS,QAAQ,8BAA8B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzD,MAAMC,QAAQ,GAAGA,CAAC;EAAEC,YAAY;EAAEC;AAAa,CAAC,KAAK;EAAAC,EAAA;EACnD,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGX,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACY,OAAO,EAAEC,UAAU,CAAC,GAAGb,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM;IAAEc;EAAY,CAAC,GAAGX,SAAS,CAAC,CAAC;EAEnCF,SAAS,CAAC,MAAM;IACd,MAAMc,UAAU,GAAG,MAAAA,CAAA,KAAY;MAC7B,IAAI;QACF,MAAMC,QAAQ,GAAG,MAAMd,KAAK,CAACe,GAAG,CAAC,iCAAiC,CAAC;QACnEN,QAAQ,CAACK,QAAQ,CAACE,IAAI,CAAC;MACzB,CAAC,CAAC,OAAOC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC/C,CAAC,SAAS;QACRN,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDE,UAAU,CAAC,CAAC;EACd,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMM,YAAY,GAAIC,MAAM,IAAK;IAC/B,OAAOR,WAAW,CAACS,QAAQ,CAACD,MAAM,CAAC;EACrC,CAAC;EAED,MAAME,cAAc,GAAIC,MAAM,IAAK;IACjC,QAAQA,MAAM;MACZ,KAAK,WAAW;QACd,OAAO,cAAc;MACvB,KAAK,MAAM;QACT,OAAO,YAAY;MACrB,KAAK,MAAM;QACT,OAAO,eAAe;MACxB,KAAK,gBAAgB;QACnB,OAAO,aAAa;MACtB;QACE,OAAO,aAAa;IACxB;EACF,CAAC;EAED,IAAIb,OAAO,EAAE;IACX,oBACEP,OAAA;MAAKqB,SAAS,EAAC,0EAA0E;MAAAC,QAAA,eACvFtB,OAAA;QAAKqB,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAC;MAAmB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrD,CAAC;EAEV;EAEA,oBACE1B,OAAA;IAAKqB,SAAS,EAAC,iEAAiE;IAAAC,QAAA,gBAC9EtB,OAAA;MAAKqB,SAAS,EAAC,8BAA8B;MAAAC,QAAA,eAC3CtB,OAAA;QAAIqB,SAAS,EAAC,qCAAqC;QAAAC,QAAA,EAAC;MAAK;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3D,CAAC,eAEN1B,OAAA;MAAKqB,SAAS,EAAC,wBAAwB;MAAAC,QAAA,EACpCjB,KAAK,CAACsB,MAAM,KAAK,CAAC,gBACjB3B,OAAA;QAAKqB,SAAS,EAAC,+BAA+B;QAAAC,QAAA,EAAC;MAE/C;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,GAENrB,KAAK,CAACuB,GAAG,CAAEC,IAAI,iBACb7B,OAAA;QAEE8B,OAAO,EAAEA,CAAA,KAAM5B,YAAY,CAAC2B,IAAI,CAAE;QAClCR,SAAS,EAAE,kFACT,CAAAlB,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAE4B,GAAG,MAAKF,IAAI,CAACE,GAAG,GAAG,yBAAyB,GAAG,EAAE,EAC9D;QAAAT,QAAA,eAEHtB,OAAA;UAAKqB,SAAS,EAAC,6BAA6B;UAAAC,QAAA,gBAC1CtB,OAAA;YAAKqB,SAAS,EAAC,UAAU;YAAAC,QAAA,gBACvBtB,OAAA;cAAKqB,SAAS,EAAC,oGAAoG;cAAAC,QAAA,EAChHO,IAAI,CAACG,MAAM,IAAIH,IAAI,CAACI,QAAQ,CAACC,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC;YAAC;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClD,CAAC,EACLV,YAAY,CAACa,IAAI,CAACE,GAAG,CAAC,iBACrB/B,OAAA;cAAKqB,SAAS,EAAC;YAAqF;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAC3G;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEN1B,OAAA;YAAKqB,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7BtB,OAAA;cAAKqB,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAChDtB,OAAA;gBAAIqB,SAAS,EAAC,4CAA4C;gBAAAC,QAAA,EACvDO,IAAI,CAACI;cAAQ;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACZ,CAAC,eACL1B,OAAA;gBAAKqB,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,eAC1CtB,OAAA;kBAAKqB,SAAS,EAAE,wBAAwBF,cAAc,CAACU,IAAI,CAACT,MAAM,CAAC;gBAAG;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1E,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN1B,OAAA;cAAKqB,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAChDtB,OAAA;gBAAGqB,SAAS,EAAC,gCAAgC;gBAAAC,QAAA,EAC1CO,IAAI,CAACT;cAAM;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX,CAAC,EACHV,YAAY,CAACa,IAAI,CAACE,GAAG,CAAC,gBACrB/B,OAAA;gBAAMqB,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,gBAEtD1B,OAAA;gBAAMqB,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EACpCO,IAAI,CAACO,QAAQ,GAAG,IAAIC,IAAI,CAACR,IAAI,CAACO,QAAQ,CAAC,CAACE,kBAAkB,CAAC,CAAC,GAAG;cAAS;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrE,CACP;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC,GAvCDG,IAAI,CAACE,GAAG;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAwCV,CACN;IACF;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACtB,EAAA,CA3GIH,QAAQ;EAAA,QAGYH,SAAS;AAAA;AAAAyC,EAAA,GAH7BtC,QAAQ;AA6Gd,eAAeA,QAAQ;AAAC,IAAAsC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}