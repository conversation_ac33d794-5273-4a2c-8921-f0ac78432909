{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\yaaa\\\\client\\\\src\\\\contexts\\\\AuthContext.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useState, useEffect } from 'react';\nimport axios from 'axios';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AuthContext = /*#__PURE__*/createContext();\nexport const useAuth = () => {\n  _s();\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n_s(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport const AuthProvider = ({\n  children\n}) => {\n  _s2();\n  const [user, setUser] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [token, setToken] = useState(localStorage.getItem('token'));\n\n  // Set up axios defaults\n  useEffect(() => {\n    if (token) {\n      axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;\n    } else {\n      delete axios.defaults.headers.common['Authorization'];\n    }\n  }, [token]);\n\n  // Check if user is logged in on app start\n  useEffect(() => {\n    const checkAuth = async () => {\n      if (token) {\n        try {\n          const response = await axios.get('http://localhost:5000/api/auth/me');\n          setUser(response.data.user);\n        } catch (error) {\n          console.error('Auth check failed:', error);\n          localStorage.removeItem('token');\n          setToken(null);\n        }\n      }\n      setLoading(false);\n    };\n    checkAuth();\n  }, [token]);\n  const login = async (email, password) => {\n    try {\n      const response = await axios.post('http://localhost:5000/api/auth/login', {\n        email,\n        password\n      });\n      const {\n        token: newToken,\n        user: userData\n      } = response.data;\n      localStorage.setItem('token', newToken);\n      setToken(newToken);\n      setUser(userData);\n      return {\n        success: true\n      };\n    } catch (error) {\n      var _error$response, _error$response$data;\n      return {\n        success: false,\n        message: ((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || 'Login failed'\n      };\n    }\n  };\n  const register = async (username, email, password) => {\n    try {\n      const response = await axios.post('http://localhost:5000/api/auth/register', {\n        username,\n        email,\n        password\n      });\n      const {\n        token: newToken,\n        user: userData\n      } = response.data;\n      localStorage.setItem('token', newToken);\n      setToken(newToken);\n      setUser(userData);\n      return {\n        success: true\n      };\n    } catch (error) {\n      var _error$response2, _error$response2$data;\n      return {\n        success: false,\n        message: ((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.message) || 'Registration failed'\n      };\n    }\n  };\n  const logout = async () => {\n    try {\n      await axios.post('http://localhost:5000/api/auth/logout');\n    } catch (error) {\n      console.error('Logout error:', error);\n    } finally {\n      localStorage.removeItem('token');\n      setToken(null);\n      setUser(null);\n    }\n  };\n  const updateUserStatus = async status => {\n    try {\n      const response = await axios.put('http://localhost:5000/api/users/status', {\n        status\n      });\n      setUser(prev => ({\n        ...prev,\n        status\n      }));\n      return {\n        success: true\n      };\n    } catch (error) {\n      var _error$response3, _error$response3$data;\n      return {\n        success: false,\n        message: ((_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : (_error$response3$data = _error$response3.data) === null || _error$response3$data === void 0 ? void 0 : _error$response3$data.message) || 'Status update failed'\n      };\n    }\n  };\n  const value = {\n    user,\n    login,\n    register,\n    logout,\n    updateUserStatus,\n    loading\n  };\n  return /*#__PURE__*/_jsxDEV(AuthContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 130,\n    columnNumber: 5\n  }, this);\n};\n_s2(AuthProvider, \"V8bE6DZSV5/nB2UMC4Uofie15PA=\");\n_c = AuthProvider;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useState", "useEffect", "axios", "jsxDEV", "_jsxDEV", "AuthContext", "useAuth", "_s", "context", "Error", "<PERSON>th<PERSON><PERSON><PERSON>", "children", "_s2", "user", "setUser", "loading", "setLoading", "token", "setToken", "localStorage", "getItem", "defaults", "headers", "common", "checkAuth", "response", "get", "data", "error", "console", "removeItem", "login", "email", "password", "post", "newToken", "userData", "setItem", "success", "_error$response", "_error$response$data", "message", "register", "username", "_error$response2", "_error$response2$data", "logout", "updateUserStatus", "status", "put", "prev", "_error$response3", "_error$response3$data", "value", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/yaaa/client/src/contexts/AuthContext.js"], "sourcesContent": ["import React, { createContext, useContext, useState, useEffect } from 'react';\nimport axios from 'axios';\n\nconst AuthContext = createContext();\n\nexport const useAuth = () => {\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n\nexport const AuthProvider = ({ children }) => {\n  const [user, setUser] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [token, setToken] = useState(localStorage.getItem('token'));\n\n  // Set up axios defaults\n  useEffect(() => {\n    if (token) {\n      axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;\n    } else {\n      delete axios.defaults.headers.common['Authorization'];\n    }\n  }, [token]);\n\n  // Check if user is logged in on app start\n  useEffect(() => {\n    const checkAuth = async () => {\n      if (token) {\n        try {\n          const response = await axios.get('http://localhost:5000/api/auth/me');\n          setUser(response.data.user);\n        } catch (error) {\n          console.error('Auth check failed:', error);\n          localStorage.removeItem('token');\n          setToken(null);\n        }\n      }\n      setLoading(false);\n    };\n\n    checkAuth();\n  }, [token]);\n\n  const login = async (email, password) => {\n    try {\n      const response = await axios.post('http://localhost:5000/api/auth/login', {\n        email,\n        password\n      });\n\n      const { token: newToken, user: userData } = response.data;\n      \n      localStorage.setItem('token', newToken);\n      setToken(newToken);\n      setUser(userData);\n      \n      return { success: true };\n    } catch (error) {\n      return {\n        success: false,\n        message: error.response?.data?.message || 'Login failed'\n      };\n    }\n  };\n\n  const register = async (username, email, password) => {\n    try {\n      const response = await axios.post('http://localhost:5000/api/auth/register', {\n        username,\n        email,\n        password\n      });\n\n      const { token: newToken, user: userData } = response.data;\n      \n      localStorage.setItem('token', newToken);\n      setToken(newToken);\n      setUser(userData);\n      \n      return { success: true };\n    } catch (error) {\n      return {\n        success: false,\n        message: error.response?.data?.message || 'Registration failed'\n      };\n    }\n  };\n\n  const logout = async () => {\n    try {\n      await axios.post('http://localhost:5000/api/auth/logout');\n    } catch (error) {\n      console.error('Logout error:', error);\n    } finally {\n      localStorage.removeItem('token');\n      setToken(null);\n      setUser(null);\n    }\n  };\n\n  const updateUserStatus = async (status) => {\n    try {\n      const response = await axios.put('http://localhost:5000/api/users/status', {\n        status\n      });\n      \n      setUser(prev => ({ ...prev, status }));\n      return { success: true };\n    } catch (error) {\n      return {\n        success: false,\n        message: error.response?.data?.message || 'Status update failed'\n      };\n    }\n  };\n\n  const value = {\n    user,\n    login,\n    register,\n    logout,\n    updateUserStatus,\n    loading\n  };\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  );\n};\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC7E,OAAOC,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,WAAW,gBAAGP,aAAa,CAAC,CAAC;AAEnC,OAAO,MAAMQ,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAMC,OAAO,GAAGT,UAAU,CAACM,WAAW,CAAC;EACvC,IAAI,CAACG,OAAO,EAAE;IACZ,MAAM,IAAIC,KAAK,CAAC,6CAA6C,CAAC;EAChE;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,EAAA,CANWD,OAAO;AAQpB,OAAO,MAAMI,YAAY,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,GAAA;EAC5C,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAACe,OAAO,EAAEC,UAAU,CAAC,GAAGhB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACiB,KAAK,EAAEC,QAAQ,CAAC,GAAGlB,QAAQ,CAACmB,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,CAAC;;EAEjE;EACAnB,SAAS,CAAC,MAAM;IACd,IAAIgB,KAAK,EAAE;MACTf,KAAK,CAACmB,QAAQ,CAACC,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC,GAAG,UAAUN,KAAK,EAAE;IACpE,CAAC,MAAM;MACL,OAAOf,KAAK,CAACmB,QAAQ,CAACC,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC;IACvD;EACF,CAAC,EAAE,CAACN,KAAK,CAAC,CAAC;;EAEX;EACAhB,SAAS,CAAC,MAAM;IACd,MAAMuB,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5B,IAAIP,KAAK,EAAE;QACT,IAAI;UACF,MAAMQ,QAAQ,GAAG,MAAMvB,KAAK,CAACwB,GAAG,CAAC,mCAAmC,CAAC;UACrEZ,OAAO,CAACW,QAAQ,CAACE,IAAI,CAACd,IAAI,CAAC;QAC7B,CAAC,CAAC,OAAOe,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;UAC1CT,YAAY,CAACW,UAAU,CAAC,OAAO,CAAC;UAChCZ,QAAQ,CAAC,IAAI,CAAC;QAChB;MACF;MACAF,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC;IAEDQ,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,CAACP,KAAK,CAAC,CAAC;EAEX,MAAMc,KAAK,GAAG,MAAAA,CAAOC,KAAK,EAAEC,QAAQ,KAAK;IACvC,IAAI;MACF,MAAMR,QAAQ,GAAG,MAAMvB,KAAK,CAACgC,IAAI,CAAC,sCAAsC,EAAE;QACxEF,KAAK;QACLC;MACF,CAAC,CAAC;MAEF,MAAM;QAAEhB,KAAK,EAAEkB,QAAQ;QAAEtB,IAAI,EAAEuB;MAAS,CAAC,GAAGX,QAAQ,CAACE,IAAI;MAEzDR,YAAY,CAACkB,OAAO,CAAC,OAAO,EAAEF,QAAQ,CAAC;MACvCjB,QAAQ,CAACiB,QAAQ,CAAC;MAClBrB,OAAO,CAACsB,QAAQ,CAAC;MAEjB,OAAO;QAAEE,OAAO,EAAE;MAAK,CAAC;IAC1B,CAAC,CAAC,OAAOV,KAAK,EAAE;MAAA,IAAAW,eAAA,EAAAC,oBAAA;MACd,OAAO;QACLF,OAAO,EAAE,KAAK;QACdG,OAAO,EAAE,EAAAF,eAAA,GAAAX,KAAK,CAACH,QAAQ,cAAAc,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBZ,IAAI,cAAAa,oBAAA,uBAApBA,oBAAA,CAAsBC,OAAO,KAAI;MAC5C,CAAC;IACH;EACF,CAAC;EAED,MAAMC,QAAQ,GAAG,MAAAA,CAAOC,QAAQ,EAAEX,KAAK,EAAEC,QAAQ,KAAK;IACpD,IAAI;MACF,MAAMR,QAAQ,GAAG,MAAMvB,KAAK,CAACgC,IAAI,CAAC,yCAAyC,EAAE;QAC3ES,QAAQ;QACRX,KAAK;QACLC;MACF,CAAC,CAAC;MAEF,MAAM;QAAEhB,KAAK,EAAEkB,QAAQ;QAAEtB,IAAI,EAAEuB;MAAS,CAAC,GAAGX,QAAQ,CAACE,IAAI;MAEzDR,YAAY,CAACkB,OAAO,CAAC,OAAO,EAAEF,QAAQ,CAAC;MACvCjB,QAAQ,CAACiB,QAAQ,CAAC;MAClBrB,OAAO,CAACsB,QAAQ,CAAC;MAEjB,OAAO;QAAEE,OAAO,EAAE;MAAK,CAAC;IAC1B,CAAC,CAAC,OAAOV,KAAK,EAAE;MAAA,IAAAgB,gBAAA,EAAAC,qBAAA;MACd,OAAO;QACLP,OAAO,EAAE,KAAK;QACdG,OAAO,EAAE,EAAAG,gBAAA,GAAAhB,KAAK,CAACH,QAAQ,cAAAmB,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBjB,IAAI,cAAAkB,qBAAA,uBAApBA,qBAAA,CAAsBJ,OAAO,KAAI;MAC5C,CAAC;IACH;EACF,CAAC;EAED,MAAMK,MAAM,GAAG,MAAAA,CAAA,KAAY;IACzB,IAAI;MACF,MAAM5C,KAAK,CAACgC,IAAI,CAAC,uCAAuC,CAAC;IAC3D,CAAC,CAAC,OAAON,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;IACvC,CAAC,SAAS;MACRT,YAAY,CAACW,UAAU,CAAC,OAAO,CAAC;MAChCZ,QAAQ,CAAC,IAAI,CAAC;MACdJ,OAAO,CAAC,IAAI,CAAC;IACf;EACF,CAAC;EAED,MAAMiC,gBAAgB,GAAG,MAAOC,MAAM,IAAK;IACzC,IAAI;MACF,MAAMvB,QAAQ,GAAG,MAAMvB,KAAK,CAAC+C,GAAG,CAAC,wCAAwC,EAAE;QACzED;MACF,CAAC,CAAC;MAEFlC,OAAO,CAACoC,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAEF;MAAO,CAAC,CAAC,CAAC;MACtC,OAAO;QAAEV,OAAO,EAAE;MAAK,CAAC;IAC1B,CAAC,CAAC,OAAOV,KAAK,EAAE;MAAA,IAAAuB,gBAAA,EAAAC,qBAAA;MACd,OAAO;QACLd,OAAO,EAAE,KAAK;QACdG,OAAO,EAAE,EAAAU,gBAAA,GAAAvB,KAAK,CAACH,QAAQ,cAAA0B,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBxB,IAAI,cAAAyB,qBAAA,uBAApBA,qBAAA,CAAsBX,OAAO,KAAI;MAC5C,CAAC;IACH;EACF,CAAC;EAED,MAAMY,KAAK,GAAG;IACZxC,IAAI;IACJkB,KAAK;IACLW,QAAQ;IACRI,MAAM;IACNC,gBAAgB;IAChBhC;EACF,CAAC;EAED,oBACEX,OAAA,CAACC,WAAW,CAACiD,QAAQ;IAACD,KAAK,EAAEA,KAAM;IAAA1C,QAAA,EAChCA;EAAQ;IAAA4C,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACW,CAAC;AAE3B,CAAC;AAAC9C,GAAA,CAxHWF,YAAY;AAAAiD,EAAA,GAAZjD,YAAY;AAAA,IAAAiD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}