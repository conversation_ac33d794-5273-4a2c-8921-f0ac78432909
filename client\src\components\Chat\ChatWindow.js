import React, { useState, useEffect, useRef } from 'react';
import axios from 'axios';
import { useSocket } from '../../contexts/SocketContext';
import { useAuth } from '../../contexts/AuthContext';
import MessageInput from './MessageInput';
import Message from './Message';

const ChatWindow = ({ selectedChat, onBack }) => {
  const [messages, setMessages] = useState([]);
  const [loading, setLoading] = useState(false);
  const messagesEndRef = useRef(null);
  const { user } = useAuth();
  const { messages: socketMessages, typingUsers } = useSocket();

  // Scroll to bottom when new messages arrive
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // Fetch messages when chat is selected
  useEffect(() => {
    if (selectedChat) {
      fetchMessages();
    }
  }, [selectedChat]);

  // Listen for new socket messages
  useEffect(() => {
    if (selectedChat && socketMessages.length > 0) {
      const newMessage = socketMessages[socketMessages.length - 1];
      if (newMessage.senderId === selectedChat._id || newMessage.senderId === user.id) {
        setMessages(prev => {
          // Avoid duplicates
          const exists = prev.some(msg => 
            msg.createdAt === newMessage.timestamp && 
            msg.content === newMessage.message
          );
          if (!exists) {
            return [...prev, {
              _id: Date.now(),
              sender: { _id: newMessage.senderId },
              content: newMessage.message,
              createdAt: newMessage.timestamp
            }];
          }
          return prev;
        });
      }
    }
  }, [socketMessages, selectedChat, user.id]);

  const fetchMessages = async () => {
    if (!selectedChat) return;
    
    setLoading(true);
    try {
      const response = await axios.get(`http://localhost:5000/api/messages/${selectedChat._id}`);
      setMessages(response.data);
      
      // Mark messages as read
      await axios.put(`http://localhost:5000/api/messages/read/${selectedChat._id}`);
    } catch (error) {
      console.error('Error fetching messages:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSendMessage = async (content) => {
    if (!selectedChat || !content.trim()) return;

    try {
      // Send via API for persistence
      const response = await axios.post('http://localhost:5000/api/messages', {
        recipientId: selectedChat._id,
        content: content.trim()
      });

      // Add to local state
      setMessages(prev => [...prev, response.data]);
    } catch (error) {
      console.error('Error sending message:', error);
    }
  };

  if (!selectedChat) {
    return (
      <div className="flex-1 flex items-center justify-center bg-whatsapp-chat-bg">
        <div className="text-center text-gray-500">
          <div className="text-6xl mb-4">💬</div>
          <h3 className="text-xl font-medium mb-2">Welcome to TinChat</h3>
          <p>Select a contact to start chatting</p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex-1 flex flex-col bg-whatsapp-chat-bg">
      {/* Chat Header */}
      <div className="bg-white border-b border-gray-200 p-4 flex items-center space-x-3">
        {onBack && (
          <button
            onClick={onBack}
            className="md:hidden p-2 hover:bg-gray-100 rounded-full"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
          </button>
        )}
        <div className="w-10 h-10 bg-whatsapp-green rounded-full flex items-center justify-center text-white font-semibold">
          {selectedChat.avatar || selectedChat.username.charAt(0).toUpperCase()}
        </div>
        <div className="flex-1">
          <h3 className="font-medium text-gray-900">{selectedChat.username}</h3>
          <p className="text-sm text-gray-500">
            {typingUsers[selectedChat._id] ? 'typing...' : selectedChat.status}
          </p>
        </div>
      </div>

      {/* Messages Area */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {loading ? (
          <div className="flex justify-center">
            <div className="text-gray-500">Loading messages...</div>
          </div>
        ) : messages.length === 0 ? (
          <div className="text-center text-gray-500">
            <p>No messages yet. Start the conversation!</p>
          </div>
        ) : (
          messages.map((message) => (
            <Message
              key={message._id}
              message={message}
              isOwn={message.sender._id === user.id}
            />
          ))
        )}
        <div ref={messagesEndRef} />
      </div>

      {/* Message Input */}
      <MessageInput
        onSendMessage={handleSendMessage}
        recipientId={selectedChat._id}
      />
    </div>
  );
};

export default ChatWindow;
